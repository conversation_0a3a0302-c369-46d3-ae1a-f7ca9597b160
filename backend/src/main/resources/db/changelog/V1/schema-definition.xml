<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create Site table -->
    <changeSet id="001-create-site-table" author="abounass">
        <createTable tableName="sites">
            <column name="site_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="city" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_selected_at" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes for better performance -->
        <createIndex tableName="sites" indexName="idx_site_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_city">
            <column name="city"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_country">
            <column name="country"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

    <!-- Insert sample data -->
    <changeSet id="002-insert-sample-sites" author="abounass">
        <insert tableName="sites">
            <column name="name" value="Workeem Paris"/>
            <column name="city" value="Paris"/>
            <column name="country" value="France"/>
            <column name="address" value="123 Avenue des Champs-Élysées, 75008 Paris"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Lyon"/>
            <column name="city" value="Lyon"/>
            <column name="country" value="France"/>
            <column name="address" value="45 Rue de la République, 69002 Lyon"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Marseille"/>
            <column name="city" value="Marseille"/>
            <column name="country" value="France"/>
            <column name="address" value="78 La Canebière, 13001 Marseille"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Bordeaux"/>
            <column name="city" value="Bordeaux"/>
            <column name="country" value="France"/>
            <column name="address" value="12 Cours de l'Intendance, 33000 Bordeaux"/>
            <column name="is_active" valueBoolean="false"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

    <!-- Create spaces table -->
    <changeSet id="3" author="abounass">
        <createTable tableName="spaces">
            <column name="space_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="capacity" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="floor" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="amenities" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes for spaces -->
        <createIndex tableName="spaces" indexName="idx_space_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_type">
            <column name="type"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>