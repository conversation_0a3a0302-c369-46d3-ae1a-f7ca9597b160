package com.workeem.workeem_api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EnableJpaAuditing(auditorAwareRef = "auditorAware")
@EnableJpaRepositories(basePackages = "com.workeem.workeem_api.business.*.infrastructure")
// @EnableCaching - DISABLED (Redis/Cache disabled)
public class WorkeemApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(WorkeemApiApplication.class, args);
	}
}
