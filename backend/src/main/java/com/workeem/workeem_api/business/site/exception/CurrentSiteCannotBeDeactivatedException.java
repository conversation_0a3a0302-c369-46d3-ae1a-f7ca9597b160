package com.workeem.workeem_api.business.site.exception;

import com.workeem.workeem_api.shared.exception.BusinessException;

/**
 * Exception thrown when trying to deactivate the currently selected site
 */
public class CurrentSiteCannotBeDeactivatedException extends BusinessException {
    
    public CurrentSiteCannotBeDeactivatedException() {
        super("CURRENT_SITE_CANNOT_BE_DEACTIVATED", 
              "Cannot deactivate the currently selected site. Please select another site first.");
    }
    
    public CurrentSiteCannotBeDeactivatedException(String siteName) {
        super("CURRENT_SITE_CANNOT_BE_DEACTIVATED", 
              String.format("Cannot deactivate site '%s' as it is currently selected. Please select another site first.", siteName));
    }
}
