package com.workeem.workeem_api.business.site.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workeem.workeem_api.business.site.domain.Site;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SiteResponseDto {

    private String id;
    private String name;
    private String city;
    private String country;
    private String address;
    private Boolean isActive;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastSelectedAt;

    /**
     * Convert Site entity to SiteResponseDto
     */
    public static SiteResponseDto fromEntity(Site site) {
        return SiteResponseDto.<SiteResponseDto>builder()
                .id(site.getSiteId().toString())
                .name(site.getName())
                .city(site.getCity())
                .country(site.getCountry())
                .address(site.getAddress())
                .isActive(site.getIsActive())
                .createdAt(site.getCreatedDate())
                .updatedAt(site.getLastModifiedDate())
                .lastSelectedAt(site.getLastSelectedAt())
                .build();
    }
}
