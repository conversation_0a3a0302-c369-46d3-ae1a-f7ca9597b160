package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceStatus;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.infrastructure.SpaceRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SpaceServiceImpl implements SpaceService {

    private final SpaceRepository spaceRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Space> getAllSpaces(String siteId) {
        log.debug("Fetching all spaces for site: {}", siteId);
        return spaceRepository.findBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getActiveSpaces(String siteId) {
        log.debug("Fetching active spaces for site: {}", siteId);
        return spaceRepository.findBySiteIdAndIsActiveTrue(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public Space getSpaceById(Long id) {
        log.debug("Fetching space with ID: {}", id);
        return spaceRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Space with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getSpacesByType(String siteId, SpaceType type) {
        log.debug("Fetching spaces by type {} for site: {}", type, siteId);
        return spaceRepository.findBySiteIdAndTypeAndIsActiveTrue(siteId, type);
    }

    @Override
    public Space createSpace(Space space) {
        log.debug("Creating new space: {}", space.getName());
        
        // Vérifier si le nom existe déjà pour ce site
        if (existsByName(space.getSiteId(), space.getName())) {
            throw new IllegalArgumentException("Space name already exists for this site");
        }
        
        // Définir les valeurs par défaut
        if (space.getStatus() == null) {
            space.setStatus(SpaceStatus.AVAILABLE);
        }
        if (space.getIsActive() == null) {
            space.setIsActive(true);
        }
        if (space.getCurrency() == null) {
            space.setCurrency("EUR");
        }
        
        return spaceRepository.save(space);
    }

    @Override
    public Space updateSpace(Long spaceId, Space space) {
        log.debug("Updating space with ID: {}", spaceId);
        
        Space existingSpace = getSpaceById(spaceId);
        
        // Vérifier si le nouveau nom existe déjà (sauf pour l'espace actuel)
        if (!existingSpace.getName().equals(space.getName()) && 
            existsByName(space.getSiteId(), space.getName())) {
            throw new IllegalArgumentException("Space name already exists for this site");
        }
        
        // Mettre à jour les champs
        existingSpace.setName(space.getName());
        existingSpace.setDescription(space.getDescription());
        existingSpace.setType(space.getType());
        existingSpace.setCapacity(space.getCapacity());
        existingSpace.setLocation(space.getLocation());
        existingSpace.setFloor(space.getFloor());
        existingSpace.setArea(space.getArea());
        existingSpace.setAmenities(space.getAmenities());
        existingSpace.setRules(space.getRules());
        existingSpace.setImages(space.getImages());
        
        // Mettre à jour les tarifs
        existingSpace.setHourlyRate(space.getHourlyRate());
        existingSpace.setDailyRate(space.getDailyRate());
        existingSpace.setWeeklyRate(space.getWeeklyRate());
        existingSpace.setMonthlyRate(space.getMonthlyRate());
        existingSpace.setCurrency(space.getCurrency());
        
        return spaceRepository.save(existingSpace);
    }

    @Override
    public void deleteSpace(Long id) {
        log.debug("Deleting space with ID: {}", id);
        
        Space space = getSpaceById(id);
        spaceRepository.delete(space);
    }

    @Override
    public Space toggleSpaceStatus(Long spaceId) {
        log.debug("Toggling status for space with ID: {}", spaceId);
        
        Space space = getSpaceById(spaceId);
        space.setIsActive(!space.getIsActive());
        
        return spaceRepository.save(space);
    }

    @Override
    public Space updateSpaceStatus(Long spaceId, String status) {
        log.debug("Updating status to {} for space with ID: {}", status, spaceId);
        
        Space space = getSpaceById(spaceId);
        space.setStatus(SpaceStatus.valueOf(status.toUpperCase()));
        
        return spaceRepository.save(space);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String siteId, String name) {
        return spaceRepository.existsBySiteIdAndNameIgnoreCase(siteId, name);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSpacesBySite(String siteId) {
        return spaceRepository.countBySiteId(siteId);
    }
}
