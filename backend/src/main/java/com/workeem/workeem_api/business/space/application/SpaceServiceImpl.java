package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.infrastructure.SpaceRepository;
import com.workeem.workeem_api.multitenancy.tenant.TenantContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SpaceServiceImpl implements SpaceService {

    private final SpaceRepository spaceRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Space> getSpaces() {
        String siteId = getCurrentSiteId();
        log.debug("Getting all spaces for site: {}", siteId);
        return spaceRepository.findBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getSpaces(SpaceType type, Boolean isActive) {
        String siteId = getCurrentSiteId();
        log.debug("Getting spaces for site: {} with filters - type: {}, active: {}", siteId, type, isActive);

        if (type != null && isActive != null) {
            if (isActive) {
                return spaceRepository.findBySiteIdAndTypeAndIsActiveTrue(siteId, type);
            } else {
                return spaceRepository.findBySiteIdAndType(siteId, type);
            }
        } else if (type != null) {
            return spaceRepository.findBySiteIdAndType(siteId, type);
        } else if (isActive != null && isActive) {
            return spaceRepository.findBySiteIdAndIsActiveTrue(siteId);
        } else {
            return spaceRepository.findBySiteId(siteId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Space getSpaceById(Long spaceId) {
        log.debug("Getting space by ID: {}", spaceId);
        return spaceRepository.findById(spaceId)
                .orElseThrow(() -> new RuntimeException("Space not found with ID: " + spaceId));
    }

    @Override
    public Space createSpace(Space space) {
        String siteId = getCurrentSiteId();
        space.setSiteId(siteId);
        
        log.debug("Creating space: {} for site: {}", space.getName(), siteId);

        // Check if name already exists for this site
        if (spaceRepository.existsBySiteIdAndNameIgnoreCase(siteId, space.getName())) {
            throw new RuntimeException("Space name already exists for this site: " + space.getName());
        }

        Space savedSpace = spaceRepository.save(space);
        log.info("Space created successfully with ID: {}", savedSpace.getSpaceId());
        return savedSpace;
    }

    @Override
    public Space updateSpace(Long spaceId, Space space) {
        log.debug("Updating space with ID: {}", spaceId);
        
        Space existingSpace = getSpaceById(spaceId);
        String siteId = getCurrentSiteId();

        // Check if name already exists for this site (excluding current space)
        if (!existingSpace.getName().equalsIgnoreCase(space.getName()) &&
            spaceRepository.existsBySiteIdAndNameIgnoreCase(siteId, space.getName())) {
            throw new RuntimeException("Space name already exists for this site: " + space.getName());
        }

        // Update fields
        if (space.getName() != null) {
            existingSpace.setName(space.getName());
        }
        if (space.getDescription() != null) {
            existingSpace.setDescription(space.getDescription());
        }
        if (space.getType() != null) {
            existingSpace.setType(space.getType());
        }
        if (space.getCapacity() != null) {
            existingSpace.setCapacity(space.getCapacity());
        }
        if (space.getHourlyRate() != null) {
            existingSpace.setHourlyRate(space.getHourlyRate());
        }
        if (space.getDailyRate() != null) {
            existingSpace.setDailyRate(space.getDailyRate());
        }
        if (space.getMonthlyRate() != null) {
            existingSpace.setMonthlyRate(space.getMonthlyRate());
        }
        if (space.getIsActive() != null) {
            existingSpace.setIsActive(space.getIsActive());
        }
        if (space.getFloor() != null) {
            existingSpace.setFloor(space.getFloor());
        }
        if (space.getAmenities() != null) {
            existingSpace.setAmenities(space.getAmenities());
        }

        Space updatedSpace = spaceRepository.save(existingSpace);
        log.info("Space updated successfully with ID: {}", spaceId);
        return updatedSpace;
    }

    @Override
    public void deleteSpace(Long spaceId) {
        log.debug("Deleting space with ID: {}", spaceId);
        
        Space space = getSpaceById(spaceId);
        spaceRepository.delete(space);
        
        log.info("Space deleted successfully with ID: {}", spaceId);
    }

    @Override
    public Space toggleSpaceStatus(Long spaceId) {
        log.debug("Toggling status for space with ID: {}", spaceId);
        
        Space space = getSpaceById(spaceId);
        space.setIsActive(!space.getIsActive());
        
        Space updatedSpace = spaceRepository.save(space);
        log.info("Space status toggled successfully for ID: {}, new status: {}", spaceId, updatedSpace.getIsActive());
        return updatedSpace;
    }

    private String getCurrentSiteId() {
        String siteId = TenantContext.getCurrentTenant();
        if (siteId == null) {
            throw new RuntimeException("No site context found");
        }
        return siteId;
    }
}
