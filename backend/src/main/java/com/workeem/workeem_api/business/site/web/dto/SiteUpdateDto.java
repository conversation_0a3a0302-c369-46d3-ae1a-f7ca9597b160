package com.workeem.workeem_api.business.site.web.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SiteUpdateDto {

    @Size(max = 100, message = "Site name must not exceed 100 characters")
    private String name;

    @Size(max = 50, message = "City must not exceed 50 characters")
    private String city;

    @Size(max = 50, message = "Country must not exceed 50 characters")
    private String country;

    @Size(max = 255, message = "Address must not exceed 255 characters")
    private String address;

    private Boolean isActive;
}
