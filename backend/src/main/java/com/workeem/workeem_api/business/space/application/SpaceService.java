package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;

import java.util.List;

public interface SpaceService {

    /**
     * Get all spaces for current site
     */
    List<Space> getSpaces();

    /**
     * Get spaces with optional filters
     */
    List<Space> getSpaces(SpaceType type, Boolean isActive);

    /**
     * Get space by ID
     */
    Space getSpaceById(Long spaceId);

    /**
     * Create new space
     */
    Space createSpace(Space space);

    /**
     * Update existing space
     */
    Space updateSpace(Long spaceId, Space space);

    /**
     * Delete space
     */
    void deleteSpace(Long spaceId);

    /**
     * Toggle space status (activate/deactivate)
     */
    Space toggleSpaceStatus(Long spaceId);
}
