package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;

import java.util.List;

public interface SpaceService {

    /**
     * Get all spaces for a specific site
     */
    List<Space> getAllSpaces(String siteId);

    /**
     * Get all active spaces for a specific site
     */
    List<Space> getActiveSpaces(String siteId);

    /**
     * Get space by ID
     */
    Space getSpaceById(Long id);

    /**
     * Get spaces by type for a specific site
     */
    List<Space> getSpacesByType(String siteId, SpaceType type);

    /**
     * Create a new space
     */
    Space createSpace(Space space);

    /**
     * Update an existing space
     */
    Space updateSpace(Long spaceId, Space space);

    /**
     * Delete a space by ID
     */
    void deleteSpace(Long id);

    /**
     * Toggle space status (activate/deactivate)
     */
    Space toggleSpaceStatus(Long spaceId);

    /**
     * Update space status
     */
    Space updateSpaceStatus(Long spaceId, String status);

    /**
     * Check if space name exists for a site
     */
    boolean existsByName(String siteId, String name);

    /**
     * Count total spaces for a site
     */
    long countSpacesBySite(String siteId);
}
