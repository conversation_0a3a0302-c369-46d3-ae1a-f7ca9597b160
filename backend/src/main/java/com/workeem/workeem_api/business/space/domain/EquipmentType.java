package com.workeem.workeem_api.business.space.domain;

/**
 * Types d'équipements disponibles
 */
public enum EquipmentType {
    
    COMPUTER("Ordinateur"),
    MONITOR("Écran"),
    PRINTER("Imprimante"),
    PROJECTOR("Projecteur"),
    WHITEBOARD("Tableau blanc"),
    TV_SCREEN("Écran TV"),
    PHONE("Téléphone"),
    WEBCAM("Webcam"),
    MICROPHONE("Microphone"),
    SPEAKERS("Haut-parleurs"),
    DESK("Bureau"),
    CHAIR("Chaise"),
    STORAGE("Rangement"),
    WIFI("WiFi"),
    ETHERNET("Ethernet");

    private final String displayName;

    EquipmentType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
