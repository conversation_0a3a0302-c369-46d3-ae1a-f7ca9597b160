package com.workeem.workeem_api.business.space.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpacePricingDto {
    
    private BigDecimal hourlyRate = BigDecimal.ZERO;
    private BigDecimal dailyRate = BigDecimal.ZERO;
    private BigDecimal weeklyRate = BigDecimal.ZERO;
    private BigDecimal monthlyRate = BigDecimal.ZERO;
    private String currency = "EUR";
    
    // Pour simplifier, on peut stocker les remises comme une chaîne JSON
    private String discounts;
}
