package com.workeem.workeem_api.business.space.domain;

import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Entity
@Table(name = "space_availability")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SpaceAvailability extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "availability_id")
    private Long availabilityId;

    @Column(name = "space_id", nullable = false)
    private Long spaceId;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "advance_booking_days", nullable = false)
    private Integer advanceBookingDays = 30;

    @Column(name = "min_booking_duration", nullable = false)
    private Integer minBookingDuration = 60; // en minutes

    @Column(name = "max_booking_duration", nullable = false)
    private Integer maxBookingDuration = 480; // en minutes

    @Column(name = "buffer_time", nullable = false)
    private Integer bufferTime = 15; // en minutes

    @Column(name = "weekly_schedule", columnDefinition = "TEXT")
    private String weeklySchedule; // JSON string

    @Column(name = "exceptions", columnDefinition = "TEXT")
    private String exceptions; // JSON string

    // Relation avec Space
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "space_id", insertable = false, updatable = false)
    private Space space;
}
