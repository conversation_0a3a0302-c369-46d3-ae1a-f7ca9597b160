package com.workeem.workeem_api.business.space.infrastructure;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpaceRepository extends JpaRepository<Space, Long> {
    
    /**
     * Find all spaces for a specific site
     */
    List<Space> findBySiteId(String siteId);
    
    /**
     * Find active spaces for a specific site
     */
    List<Space> findBySiteIdAndIsActiveTrue(String siteId);
    
    /**
     * Find spaces by type for a specific site
     */
    List<Space> findBySiteIdAndType(String siteId, SpaceType type);
    
    /**
     * Find spaces by type and active status for a specific site
     */
    List<Space> findBySiteIdAndTypeAndIsActiveTrue(String siteId, SpaceType type);
    
    /**
     * Check if space name exists for a site (for validation)
     */
    boolean existsBySiteIdAndNameIgnoreCase(String siteId, String name);
    
    /**
     * Count total spaces for a site
     */
    long countBySiteId(String siteId);
    
    /**
     * Count active spaces for a site
     */
    long countBySiteIdAndIsActiveTrue(String siteId);
}
