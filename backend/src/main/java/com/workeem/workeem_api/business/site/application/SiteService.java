package com.workeem.workeem_api.business.site.application;

import com.workeem.workeem_api.business.site.domain.Site;

import java.util.List;

public interface SiteService {

    /**
     * Get all sites
     */
    List<Site> getAllSites();

    /**
     * Get site by ID
     */
    Site getSiteById(Long id);

    /**
     * Create a new site
     */
    Site createSite(Site site);

    /**
     * Update an existing site
     */
    Site updateSite(Long siteId, Site site);

    /**
     * Delete a site by ID
     */
    void deleteSiteById(Long id);

    /**
     * Toggle site status (activate/deactivate)
     */
    Site toggleSiteStatus(Long siteId);

    /**
     * Select a site as current (updates last_selected_at)
     */
    Site selectSite(Long siteId);

    /**
     * Get the current selected site (most recently selected)
     */
    Site getCurrentSite();
}
