package com.workeem.workeem_api.business.space.web.dto;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceRequestDto {

    @NotBlank(message = "Space name is required")
    @Size(max = 100, message = "Space name must not exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @NotNull(message = "Space type is required")
    private SpaceType type;

    @Min(value = 1, message = "Capacity must be at least 1")
    private Integer capacity;

    @Min(value = 0, message = "Hourly rate cannot be negative")
    private Double hourlyRate;

    @Min(value = 0, message = "Daily rate cannot be negative")
    private Double dailyRate;

    @Min(value = 0, message = "Monthly rate cannot be negative")
    private Double monthlyRate;

    private Boolean isActive = true;

    @Size(max = 50, message = "Floor must not exceed 50 characters")
    private String floor;

    @Size(max = 1000, message = "Amenities must not exceed 1000 characters")
    private String amenities;

    /**
     * Convert DTO to Entity
     */
    public Space toEntity(String siteId) {
        Space space = new Space();
        space.setSiteId(siteId);
        space.setName(this.name);
        space.setDescription(this.description);
        space.setType(this.type);
        space.setCapacity(this.capacity);
        space.setHourlyRate(this.hourlyRate);
        space.setDailyRate(this.dailyRate);
        space.setMonthlyRate(this.monthlyRate);
        space.setIsActive(this.isActive != null ? this.isActive : true);
        space.setFloor(this.floor);
        space.setAmenities(this.amenities);
        return space;
    }
}
