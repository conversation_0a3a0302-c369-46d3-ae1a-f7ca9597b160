package com.workeem.workeem_api.business.site.infrastructure;

import com.workeem.workeem_api.business.site.domain.Site;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SiteRepository extends JpaRepository<Site, Long> {

    /**
     * Check if site name exists (for validation)
     */
    boolean existsByNameIgnoreCase(String name);

    /**
     * Find all sites ordered by last selected date descending
     */
    List<Site> findAllByOrderByLastSelectedAtDesc();
}
