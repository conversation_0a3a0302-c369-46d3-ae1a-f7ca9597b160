package com.workeem.workeem_api.business.space.domain;

/**
 * Statuts disponibles pour un espace
 */
public enum SpaceStatus {
    
    /**
     * Espace disponible pour réservation
     */
    AVAILABLE("Disponible"),
    
    /**
     * Espace actuellement occupé
     */
    OCCUPIED("Occupé"),
    
    /**
     * Espace en maintenance
     */
    MAINTENANCE("En maintenance"),
    
    /**
     * Espace hors service
     */
    OUT_OF_ORDER("Hors service"),
    
    /**
     * Espace réservé
     */
    RESERVED("Réservé");

    private final String displayName;

    SpaceStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
