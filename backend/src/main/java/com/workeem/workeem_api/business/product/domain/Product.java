package com.workeem.workeem_api.business.product.domain;

import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serializable;


@Entity
@Table(name = "tbl_product")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Product extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "product_id")
    private Integer productId;

    @Size(max = 50)
    @Column(name = "product_name", nullable = false)
    private String productName;

    @Size(max = 10)
    @Column(name = "quantity", nullable = false)
    private String quantity;

    @Size(max = 3)
    @Column(name = "size", nullable = false, unique = true)
    private String size;
}
