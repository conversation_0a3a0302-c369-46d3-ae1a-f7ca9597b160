package com.workeem.workeem_api.business.space.web.dto;

import com.workeem.workeem_api.business.space.domain.EquipmentStatus;
import com.workeem.workeem_api.business.space.domain.EquipmentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentDto {
    
    private String name;
    private EquipmentType type;
    private String brand;
    private String model;
    private Integer quantity = 1;
    private EquipmentStatus status = EquipmentStatus.WORKING;
    private String description;
}
