package com.workeem.workeem_api.business.site.application;

import com.workeem.workeem_api.business.site.domain.Site;
import com.workeem.workeem_api.business.site.exception.CurrentSiteCannotBeDeactivatedException;
import com.workeem.workeem_api.business.site.infrastructure.SiteRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SiteServiceImpl implements SiteService {

    private final SiteRepository siteRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Site> getAllSites() {
        log.debug("Fetching all sites");
        return siteRepository.findAll();
    }



    @Override
    @Transactional(readOnly = true)
    public Site getSiteById(Long id) {
        log.debug("Fetching site with ID: {}", id);
        return siteRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Site with ID " + id + " not found"));
    }

    @Override
    public Site createSite(Site site) {
        log.debug("Creating new site: {}", site.getName());
        
        // Check if site name already exists
        if (siteRepository.existsByNameIgnoreCase(site.getName())) {
            throw new DataIntegrityViolationException("Site with name '" + site.getName() + "' already exists");
        }

        // Set default values
        if (site.getIsActive() == null) {
            site.setIsActive(true);
        }

        Site savedSite = siteRepository.save(site);
        log.info("Site created successfully with ID: {}", savedSite.getSiteId());
        return savedSite;
    }

    @Override
    public Site updateSite(Long siteId, Site site) {
        log.debug("Updating site with ID: {}", siteId);
        
        Site existingSite = getSiteById(siteId);

        // Check if new name conflicts with existing sites (excluding current site)
        if (!existingSite.getName().equalsIgnoreCase(site.getName()) && 
            siteRepository.existsByNameIgnoreCase(site.getName())) {
            throw new DataIntegrityViolationException("Site with name '" + site.getName() + "' already exists");
        }

        // Update fields
        existingSite.setName(site.getName());
        existingSite.setCity(site.getCity());
        existingSite.setCountry(site.getCountry());
        existingSite.setAddress(site.getAddress());

        if (site.getIsActive() != null) {
            // Vérifier si on essaie de désactiver le site actuellement sélectionné
            if (!site.getIsActive() && existingSite.getIsActive() && isCurrentlySelectedSite(existingSite)) {
                throw new CurrentSiteCannotBeDeactivatedException(existingSite.getName());
            }
            existingSite.setIsActive(site.getIsActive());
        }

        Site updatedSite = siteRepository.save(existingSite);
        log.info("Site updated successfully with ID: {}", updatedSite.getSiteId());
        return updatedSite;
    }

    @Override
    public void deleteSiteById(Long id) {
        log.debug("Deleting site with ID: {}", id);
        
        if (!siteRepository.existsById(id)) {
            throw new EntityNotFoundException("Site with ID " + id + " not found");
        }

        siteRepository.deleteById(id);
        log.info("Site deleted successfully with ID: {}", id);
    }

    @Override
    public Site toggleSiteStatus(Long siteId) {
        log.debug("Toggling status for site with ID: {}", siteId);

        Site site = getSiteById(siteId);

        // Vérifier si on essaie de désactiver le site actuellement sélectionné
        if (site.getIsActive() && isCurrentlySelectedSite(site)) {
            throw new CurrentSiteCannotBeDeactivatedException(site.getName());
        }

        site.setIsActive(!site.getIsActive());

        Site updatedSite = siteRepository.save(site);
        log.info("Site status toggled successfully for ID: {}, new status: {}", siteId, updatedSite.getIsActive());
        return updatedSite;
    }

    /**
     * Check if the given site is currently selected (most recent lastSelectedAt)
     */
    private boolean isCurrentlySelectedSite(Site site) {
        Site currentSite = getCurrentSite();
        return currentSite != null && currentSite.getSiteId().equals(site.getSiteId());
    }

    @Override
    public Site selectSite(Long siteId) {
        log.debug("Selecting site with ID: {}", siteId);

        Site site = getSiteById(siteId);
        site.setLastSelectedAt(LocalDateTime.now());

        Site selectedSite = siteRepository.save(site);
        log.info("Site selected successfully with ID: {}", siteId);
        return selectedSite;
    }

    @Override
    public Site getCurrentSite() {
        log.debug("Getting current selected site");

        List<Site> sites = siteRepository.findAllByOrderByLastSelectedAtDesc();

        if (sites.isEmpty()) {
            log.warn("No sites found");
            return null;
        }

        // Return the most recently selected site, or the first active site if none selected
        Site currentSite = sites.stream()
                .filter(site -> site.getLastSelectedAt() != null)
                .findFirst()
                .orElse(sites.stream()
                        .filter(Site::getIsActive)
                        .findFirst()
                        .orElse(sites.get(0)));

        log.info("Current site retrieved: {}", currentSite.getSiteId());
        return currentSite;
    }
}
