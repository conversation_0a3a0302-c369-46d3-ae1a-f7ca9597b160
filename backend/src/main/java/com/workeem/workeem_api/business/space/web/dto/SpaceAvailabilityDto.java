package com.workeem.workeem_api.business.space.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceAvailabilityDto {
    
    private Boolean isActive = true;
    private Integer advanceBookingDays = 30;
    private Integer minBookingDuration = 60; // en minutes
    private Integer maxBookingDuration = 480; // en minutes
    private Integer bufferTime = 15; // en minutes
    
    // Pour simplifier, on peut stocker le planning comme une chaîne JSON
    private String weeklySchedule;
    private String exceptions;
}
