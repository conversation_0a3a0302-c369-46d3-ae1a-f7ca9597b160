package com.workeem.workeem_api.business.product.application;

import com.workeem.workeem_api.business.product.domain.Product;

import java.util.List;


public interface ProductService {
    List<Product> getAllProduct();

    void deleteProductById(Integer id);

    void createProduct(Product product);

    Product getProductById(Integer id);

    void updateProduct(Integer productId, Product product);
}
