package com.workeem.workeem_api.business.site.web;

import com.workeem.workeem_api.business.site.application.SiteService;
import com.workeem.workeem_api.business.site.domain.Site;
import com.workeem.workeem_api.business.site.web.dto.SiteRequestDto;
import com.workeem.workeem_api.business.site.web.dto.SiteResponseDto;
import com.workeem.workeem_api.business.site.web.dto.SiteUpdateDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/sites")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class SiteController implements Serializable {

    private final SiteService siteService;

    /**
     * Get all sites
     */
    @GetMapping
    public ResponseEntity<List<SiteResponseDto>> getAllSites() {
        log.debug("GET /api/sites");

        List<Site> sites = siteService.getAllSites();
        List<SiteResponseDto> siteDtos = sites.stream()
                .map(SiteResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(siteDtos);
    }

    /**
     * Get site by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<SiteResponseDto> getSiteById(@PathVariable Long id) {
        log.debug("GET /api/sites/{}", id);
        
        Site site = siteService.getSiteById(id);
        SiteResponseDto siteDto = SiteResponseDto.fromEntity(site);
        return ResponseEntity.ok(siteDto);
    }

    /**
     * Create a new site
     */
    @PostMapping
    public ResponseEntity<SiteResponseDto> createSite(@Valid @RequestBody SiteRequestDto siteRequestDto) {
        log.debug("POST /api/sites - Creating site: {}", siteRequestDto.getName());
        
        Site site = siteRequestDto.toEntity();
        Site createdSite = siteService.createSite(site);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(createdSite);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing site
     */
    @PutMapping("/{id}")
    public ResponseEntity<SiteResponseDto> updateSite(@PathVariable Long id, @Valid @RequestBody SiteUpdateDto siteUpdateDto) {
        log.debug("PUT /api/sites/{} - Updating site", id);

        // Récupérer le site existant
        Site existingSite = siteService.getSiteById(id);

        // Mettre à jour seulement les champs fournis
        if (siteUpdateDto.getName() != null) {
            existingSite.setName(siteUpdateDto.getName());
        }
        if (siteUpdateDto.getCity() != null) {
            existingSite.setCity(siteUpdateDto.getCity());
        }
        if (siteUpdateDto.getCountry() != null) {
            existingSite.setCountry(siteUpdateDto.getCountry());
        }
        if (siteUpdateDto.getAddress() != null) {
            existingSite.setAddress(siteUpdateDto.getAddress());
        }
        if (siteUpdateDto.getIsActive() != null) {
            existingSite.setIsActive(siteUpdateDto.getIsActive());
        }

        Site updatedSite = siteService.updateSite(id, existingSite);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(updatedSite);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a site
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSite(@PathVariable Long id) {
        log.debug("DELETE /api/sites/{}", id);
        
        siteService.deleteSiteById(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Toggle site status (activate/deactivate)
     */
    @PatchMapping("/{id}/toggle-status")
    public ResponseEntity<SiteResponseDto> toggleSiteStatus(@PathVariable Long id) {
        log.debug("PATCH /api/sites/{}/toggle-status", id);

        Site updatedSite = siteService.toggleSiteStatus(id);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(updatedSite);
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Select a site as current
     */
    @PostMapping("/{id}/select")
    public ResponseEntity<SiteResponseDto> selectSite(@PathVariable Long id) {
        log.debug("POST /api/sites/{}/select", id);

        Site selectedSite = siteService.selectSite(id);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(selectedSite);
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Get current selected site
     */
    @GetMapping("/current")
    public ResponseEntity<SiteResponseDto> getCurrentSite() {
        log.debug("GET /api/sites/current");

        Site currentSite = siteService.getCurrentSite();
        if (currentSite == null) {
            return ResponseEntity.noContent().build();
        }

        SiteResponseDto responseDto = SiteResponseDto.fromEntity(currentSite);
        return ResponseEntity.ok(responseDto);
    }
}
