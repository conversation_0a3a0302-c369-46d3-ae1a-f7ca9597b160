package com.workeem.workeem_api.business.space.domain;

import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "spaces")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Space extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "space_id")
    private Long spaceId;

    @Column(name = "site_id", nullable = false)
    private String siteId;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private SpaceType type;

    @Column(name = "capacity")
    private Integer capacity;

    @Column(name = "location")
    private String location;

    @Column(name = "floor")
    private String floor;

    @Column(name = "area", precision = 10, scale = 2)
    private BigDecimal area; // en m²

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private SpaceStatus status = SpaceStatus.AVAILABLE;

    @Column(name = "images", columnDefinition = "TEXT")
    private String images; // JSON string

    @Column(name = "amenities", length = 1000)
    private String amenities; // JSON string

    @Column(name = "rules", columnDefinition = "TEXT")
    private String rules; // JSON string

    @Column(name = "hourly_rate")
    private Double hourlyRate;

    @Column(name = "daily_rate")
    private Double dailyRate;

    @Column(name = "weekly_rate")
    private Double weeklyRate;

    @Column(name = "monthly_rate")
    private Double monthlyRate;

    @Column(name = "currency", length = 10)
    private String currency = "EUR";

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    // Relations - Commentées temporairement pour éviter les conflits de mapping
    // @OneToMany(mappedBy = "space", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    // private List<Equipment> equipment;

    // @OneToOne(mappedBy = "space", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    // private SpaceAvailability availability;

    // @OneToOne(mappedBy = "space", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    // private SpacePricing pricing;
}
