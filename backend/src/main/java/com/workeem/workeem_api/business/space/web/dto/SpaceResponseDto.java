package com.workeem.workeem_api.business.space.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceResponseDto {

    private Long id;
    private String siteId;
    private String name;
    private String description;
    private SpaceType type;
    private String typeDisplayName;
    private Integer capacity;
    private Double hourlyRate;
    private Double dailyRate;
    private Double monthlyRate;
    private Boolean isActive;
    private String floor;
    private String amenities;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Convert Entity to DTO
     */
    public static SpaceResponseDto fromEntity(Space space) {
        return SpaceResponseDto.builder()
                .id(space.getSpaceId())
                .siteId(space.getSiteId())
                .name(space.getName())
                .description(space.getDescription())
                .type(space.getType())
                .typeDisplayName(space.getType().getDisplayName())
                .capacity(space.getCapacity())
                .hourlyRate(space.getHourlyRate())
                .dailyRate(space.getDailyRate())
                .monthlyRate(space.getMonthlyRate())
                .isActive(space.getIsActive())
                .floor(space.getFloor())
                .amenities(space.getAmenities())
                .createdAt(space.getCreatedDate())
                .updatedAt(space.getLastModifiedDate())
                .build();
    }
}
