package com.workeem.workeem_api.business.space.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceStatus;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceResponseDto {

    private Long id;
    private String siteId;
    private String name;
    private String description;
    private SpaceType type;
    private String typeDisplayName;
    private Integer capacity;
    private String location;
    private String floor;
    private BigDecimal area;
    private SpaceStatus status;
    private String statusDisplayName;
    private List<String> images;
    private List<String> amenities;
    private List<String> rules;

    // Pricing
    private Double hourlyRate;
    private Double dailyRate;
    private Double weeklyRate;
    private Double monthlyRate;
    private String currency;

    private Boolean isActive;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Convert Entity to DTO
     */
    public static SpaceResponseDto fromEntity(Space space) {
        return SpaceResponseDto.builder()
                .id(space.getSpaceId())
                .siteId(space.getSiteId())
                .name(space.getName())
                .description(space.getDescription())
                .type(space.getType())
                .typeDisplayName(space.getType() != null ? space.getType().getDisplayName() : null)
                .capacity(space.getCapacity())
                .location(space.getLocation())
                .floor(space.getFloor())
                .area(space.getArea())
                .status(space.getStatus())
                .statusDisplayName(space.getStatus() != null ? space.getStatus().getDisplayName() : null)
                .images(space.getImages() != null && !space.getImages().isEmpty() ?
                    Arrays.asList(space.getImages().split(",")) : null)
                .amenities(space.getAmenities() != null && !space.getAmenities().isEmpty() ?
                    Arrays.asList(space.getAmenities().split(",")) : null)
                .rules(space.getRules() != null && !space.getRules().isEmpty() ?
                    Arrays.asList(space.getRules().split(",")) : null)
                .hourlyRate(space.getHourlyRate())
                .dailyRate(space.getDailyRate())
                .weeklyRate(space.getWeeklyRate())
                .monthlyRate(space.getMonthlyRate())
                .currency(space.getCurrency())
                .isActive(space.getIsActive())
                .createdAt(space.getCreatedDate())
                .updatedAt(space.getLastModifiedDate())
                .build();
    }
}
