package com.workeem.workeem_api.business.space.domain;

import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "space_pricing")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SpacePricing extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "pricing_id")
    private Long pricingId;

    @Column(name = "space_id", nullable = false)
    private Long spaceId;

    @Column(name = "hourly_rate", nullable = false, precision = 10, scale = 2)
    private BigDecimal hourlyRate = BigDecimal.ZERO;

    @Column(name = "daily_rate", nullable = false, precision = 10, scale = 2)
    private BigDecimal dailyRate = BigDecimal.ZERO;

    @Column(name = "weekly_rate", nullable = false, precision = 10, scale = 2)
    private BigDecimal weeklyRate = BigDecimal.ZERO;

    @Column(name = "monthly_rate", nullable = false, precision = 10, scale = 2)
    private BigDecimal monthlyRate = BigDecimal.ZERO;

    @Column(name = "currency", nullable = false, length = 10)
    private String currency = "EUR";

    @Column(name = "discounts", columnDefinition = "TEXT")
    private String discounts; // JSON string

    // Relation avec Space
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "space_id", insertable = false, updatable = false)
    private Space space;
}
