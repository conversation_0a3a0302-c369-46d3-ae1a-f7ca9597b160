package com.workeem.workeem_api.business.site.web.dto;

import com.workeem.workeem_api.business.site.domain.Site;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SiteRequestDto {

    @NotBlank(message = "Site name is required")
    @Size(max = 100, message = "Site name must not exceed 100 characters")
    private String name;

    @Size(max = 50, message = "City must not exceed 50 characters")
    private String city;

    @Size(max = 50, message = "Country must not exceed 50 characters")
    private String country;

    @Size(max = 255, message = "Address must not exceed 255 characters")
    private String address;

    private Boolean isActive;

    /**
     * Convert SiteRequestDto to Site entity
     */
    public Site toEntity() {
        return Site.<Site>builder()
                .name(this.name)
                .city(this.city)
                .country(this.country)
                .address(this.address)
                .isActive(this.isActive != null ? this.isActive : true)
                .build();
    }
}
