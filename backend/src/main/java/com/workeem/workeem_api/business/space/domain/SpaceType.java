package com.workeem.workeem_api.business.space.domain;

/**
 * Types d'espaces disponibles dans un site de coworking
 */
public enum SpaceType {
    
    /**
     * Bureau privé fermé
     */
    PRIVATE_OFFICE("Bureau privé"),
    
    /**
     * Poste de travail en espace ouvert
     */
    HOT_DESK("Hot desk"),
    
    /**
     * Poste de travail dédié
     */
    DEDICATED_DESK("Poste dédié"),
    
    /**
     * Salle de réunion
     */
    MEETING_ROOM("Salle de réunion"),
    
    /**
     * Salle de conférence
     */
    CONFERENCE_ROOM("Salle de conférence"),
    
    /**
     * Espace événementiel
     */
    EVENT_SPACE("Espace événementiel"),
    
    /**
     * Cabine téléphonique
     */
    PHONE_BOOTH("Cabine téléphonique"),
    
    /**
     * Espace détente
     */
    LOUNGE("Espace détente");

    private final String displayName;

    SpaceType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
