package com.workeem.workeem_api.multitenancy.tenant.infrastructure;

import com.nimbusds.oauth2.sdk.util.StringUtils;
import com.workeem.workeem_api.multitenancy.master.infrastructure.TenantContext;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.beans.factory.annotation.Value;


public class CurrentTenantIdentifierResolverImpl implements CurrentTenantIdentifierResolver {

    @Value("${multitenancy.http.defaultTenantId}")
    private String defaultTenantId;

    @Override
    public String resolveCurrentTenantIdentifier() {
        String tenant = TenantContext.getCurrentTenant();
        return StringUtils.isNotBlank(tenant) ? tenant : defaultTenantId;
    }

    @Override
    public boolean validateExistingCurrentSessions() {
        return true;
    }
}
