package com.workeem.workeem_api.multitenancy.master.web;

import com.workeem.workeem_api.multitenancy.master.application.MasterTenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/public/tenant")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class TenantVerificationController {

    private final MasterTenantService masterTenantService;

    /**
     * Vérifier si un tenant existe par son nom (slug)
     */
    @GetMapping("/verify/{tenantName}")
    public ResponseEntity<TenantVerificationResponse> verifyTenant(@PathVariable String tenantName) {
        log.debug("Vérification du tenant: {}", tenantName);
        
        try {
            boolean exists = masterTenantService.tenantExistsByName(tenantName);
            
            if (exists) {
                String tenantId = masterTenantService.getTenantIdByName(tenantName);
                return ResponseEntity.ok(new TenantVerificationResponse(true, tenantId, tenantName));
            } else {
                return ResponseEntity.ok(new TenantVerificationResponse(false, null, null));
            }
        } catch (Exception e) {
            log.error("Erreur lors de la vérification du tenant: {}", tenantName, e);
            return ResponseEntity.ok(new TenantVerificationResponse(false, null, null));
        }
    }

    /**
     * DTO de réponse pour la vérification de tenant
     */
    public static class TenantVerificationResponse {
        private boolean exists;
        private String tenantId;
        private String tenantName;

        public TenantVerificationResponse(boolean exists, String tenantId, String tenantName) {
            this.exists = exists;
            this.tenantId = tenantId;
            this.tenantName = tenantName;
        }

        // Getters
        public boolean isExists() { return exists; }
        public String getTenantId() { return tenantId; }
        public String getTenantName() { return tenantName; }
    }
}
