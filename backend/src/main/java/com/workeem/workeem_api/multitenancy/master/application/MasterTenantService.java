package com.workeem.workeem_api.multitenancy.master.application;

import com.workeem.workeem_api.multitenancy.master.domain.Tenant;
import com.workeem.workeem_api.multitenancy.master.infrastructure.MasterTenantRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MasterTenantService {

    private final MasterTenantRepository masterTenantRepository;

    /**
     * Vérifier si un tenant existe par son nom
     */
    @Transactional(readOnly = true)
    public boolean tenantExistsByName(String tenantName) {
        log.debug("Vérification de l'existence du tenant: {}", tenantName);
        return masterTenantRepository.existsByTenantNameIgnoreCase(tenantName);
    }

    /**
     * Récupérer l'ID du tenant par son nom
     */
    @Transactional(readOnly = true)
    public String getTenantIdByName(String tenantName) {
        log.debug("Récupération de l'ID du tenant: {}", tenantName);
        return masterTenantRepository.findByTenantNameIgnoreCase(tenantName)
                .map(Tenant::getTenantId)
                .orElse(null);
    }

    /**
     * Récupérer un tenant par son nom
     */
    @Transactional(readOnly = true)
    public Tenant getTenantByName(String tenantName) {
        log.debug("Récupération du tenant: {}", tenantName);
        return masterTenantRepository.findByTenantNameIgnoreCase(tenantName)
                .orElse(null);
    }
}
