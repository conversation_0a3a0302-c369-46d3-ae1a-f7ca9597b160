package com.workeem.workeem_api.multitenancy.config;

import com.workeem.workeem_api.multitenancy.master.infrastructure.TenantContext;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component("tenantAwareKeyGenerator")
public class TenantAwareKeyGenerator implements KeyGenerator {

    @Override
    public Object generate(Object target, Method method, Object... params) {
        // Récupérer l'identifiant du tenant courant
        String tenantId = TenantContext.getCurrentTenant();
        if (tenantId == null) {
            throw new IllegalStateException("Tenant ID is not set in TenantContext");
        }

        // Construire la clé tenant-specific pour Redis
        StringBuilder key = new StringBuilder(tenantId);

        for (Object param : params) {
            key.append(":").append(param);
        }

        return key.toString();
    }
}
