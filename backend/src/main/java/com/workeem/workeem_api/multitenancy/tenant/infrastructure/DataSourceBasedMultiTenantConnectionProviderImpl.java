package com.workeem.workeem_api.multitenancy.tenant.infrastructure;

import com.workeem.workeem_api.multitenancy.master.infrastructure.TenantContext;
import com.workeem.workeem_api.multitenancy.master.domain.Tenant;
import com.workeem.workeem_api.multitenancy.master.infrastructure.MasterTenantRepository;
import com.workeem.workeem_api.shared.infrastructure.DataSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.jdbc.connections.spi.AbstractDataSourceBasedMultiTenantConnectionProviderImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import javax.sql.DataSource;
import java.io.Serial;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;


@Configuration
@Slf4j
public class DataSourceBasedMultiTenantConnectionProviderImpl extends AbstractDataSourceBasedMultiTenantConnectionProviderImpl {

    @Serial
    private static final long serialVersionUID = 1L;

    private final Map<String, DataSource> dataSourcesMtApp = new TreeMap<>();

    @Autowired
    private MasterTenantRepository masterTenantRepository;

    @Autowired
    private DataSourceUtil dataSourceUtil;

    @Override
    protected DataSource selectAnyDataSource() {
        // This method is called more than once. So check if the data source map
        // is empty. If it is then rescan master_tenant table for all tenant
        if (dataSourcesMtApp.isEmpty()) {
            List<Tenant> tenants = masterTenantRepository.findAll();
            log.info("selectAnyDataSource() method call...Total tenants: {}", tenants.size());
            for (Tenant tenant : tenants) {
                dataSourcesMtApp.put(tenant.getTenantId(), dataSourceUtil.createAndConfigureDataSource(tenant));
            }
        }

        if (dataSourcesMtApp.isEmpty()) {
            log.error("Data source map is empty even after loading tenants.");
            throw new IllegalStateException("No data sources configured.");
        }
        return this.dataSourcesMtApp.values().iterator().next();
    }

    @Override
    protected DataSource selectDataSource(Object o) {
        // If the requested tenant id is not present check for it in the master
        // database 'master_tenant' table
        String tenantIdentifier = o.toString();
        tenantIdentifier = initializeTenantIfLost(tenantIdentifier);
        if (tenantIdentifier == null || tenantIdentifier.isEmpty()) {
            throw new UsernameNotFoundException("Tenant identifier is missing or invalid.");
        }

        if (!this.dataSourcesMtApp.containsKey(tenantIdentifier)) {
            List<Tenant> tenants = masterTenantRepository.findAll();
            log.info("selectDataSource() method call...Tenant:{} Total tenants: {}", tenantIdentifier, tenants.size());
            for (Tenant tenant : tenants) {
                dataSourcesMtApp.put(tenant.getDbName(), dataSourceUtil.createAndConfigureDataSource(tenant));
            }
        }
        //check again if tenant exist in map after rescan master_db, if not, throw UsernameNotFoundException
        //TODO: Verify if new tenant is taken into consideration just after adding it dynamically
        if (!this.dataSourcesMtApp.containsKey(tenantIdentifier)) {
            log.warn("Trying to get tenant:{} which was not found in master db after rescan", tenantIdentifier);
            throw new UsernameNotFoundException(String.format("Tenant not found after rescan, tenant=%s", tenantIdentifier));
        }
        return this.dataSourcesMtApp.get(tenantIdentifier);
    }

    private String initializeTenantIfLost(String tenantIdentifier) {
        if (!Objects.equals(tenantIdentifier, TenantContext.getCurrentTenant())) {
            tenantIdentifier = TenantContext.getCurrentTenant();
        }
        return tenantIdentifier;
    }
}
