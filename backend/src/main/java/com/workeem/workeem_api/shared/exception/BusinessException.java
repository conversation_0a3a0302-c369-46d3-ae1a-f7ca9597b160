package com.workeem.workeem_api.shared.exception;

/**
 * Base class for business logic exceptions
 * These exceptions represent business rule violations and should be handled gracefully
 */
public abstract class BusinessException extends RuntimeException {
    
    private final String errorCode;
    
    /**
     * Constructor with error code and message
     * 
     * @param errorCode Unique error code for this business exception
     * @param message Human-readable error message
     */
    protected BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * Constructor with error code, message and cause
     * 
     * @param errorCode Unique error code for this business exception
     * @param message Human-readable error message
     * @param cause The underlying cause of this exception
     */
    protected BusinessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * Get the error code associated with this business exception
     * 
     * @return The error code
     */
    public String getErrorCode() {
        return errorCode;
    }
}
