export interface Invoice {
  id: string;
  invoiceNumber: string;
  memberId: string;
  memberName: string;
  memberEmail: string;
  memberCompany?: string;
  
  // Dates
  issueDate: Date;
  dueDate: Date;
  paidDate?: Date;
  
  // Statut
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  
  // Montants
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  total: number;
  
  // Détails
  items: InvoiceItem[];
  
  // Métadonnées
  notes?: string;
  paymentMethod?: 'card' | 'transfer' | 'cash' | 'check';
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  type: 'subscription' | 'reservation' | 'service' | 'product';
  period?: {
    start: Date;
    end: Date;
  };
}

export interface BillingStats {
  totalRevenue: number;
  monthlyRevenue: number;
  pendingAmount: number;
  overdueAmount: number;
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoices: number;
  overdueInvoices: number;
}

export interface PaymentHistory {
  id: string;
  invoiceId: string;
  amount: number;
  method: 'card' | 'transfer' | 'cash' | 'check';
  date: Date;
  reference?: string;
  notes?: string;
}
