import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Site } from '../models/site.model';

export interface SiteCreateRequest {
  name: string;
  city: string;
  country: string;
  address: string;
  isActive?: boolean;
}

export interface SiteUpdateRequest {
  name: string;
  city: string;
  country: string;
  address: string;
  isActive?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private readonly baseUrl = 'http://localhost:8080/api';

  constructor(private http: HttpClient) {}

  // Sites endpoints
  getSites(): Observable<Site[]> {
    return this.http.get<Site[]>(`${this.baseUrl}/sites`);
  }

  getSiteById(id: string): Observable<Site> {
    return this.http.get<Site>(`${this.baseUrl}/sites/${id}`);
  }

  createSite(site: SiteCreateRequest): Observable<Site> {
    return this.http.post<Site>(`${this.baseUrl}/sites`, site);
  }

  updateSite(id: string, site: SiteUpdateRequest): Observable<Site> {
    return this.http.put<Site>(`${this.baseUrl}/sites/${id}`, site);
  }

  deleteSite(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/sites/${id}`);
  }

  toggleSiteStatus(id: string): Observable<Site> {
    return this.http.patch<Site>(`${this.baseUrl}/sites/${id}/toggle-status`, {});
  }
}
