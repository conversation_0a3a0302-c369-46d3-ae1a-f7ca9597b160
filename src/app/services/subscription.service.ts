import { Injectable, signal } from '@angular/core';
import { Observable, of, delay, map } from 'rxjs';
import {
  SubscriptionPlan,
  UserSubscription,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  AssignSubscriptionRequest,
  SubscriptionAlert,
  SubscriptionType,
  SubscriptionStatus,
  MembershipType
} from '../models/subscription.model';

@Injectable({
  providedIn: 'root'
})
export class SubscriptionService {

  // Données mockées pour les plans d'abonnement
  private mockPlans: SubscriptionPlan[] = [
    {
      id: '1',
      name: 'Étudiant Journalier',
      description: 'Accès journalier pour étudiants',
      type: SubscriptionType.DAILY,
      membershipTypes: [MembershipType.STUDENT],
      price: 50,
      currency: 'MAD ',
      duration: 1,
      rights: {
        maxReservationsPerDay: 2,
        maxReservationsPerWeek: 10,
        maxReservationsPerMonth: 30,
        allowedTimeSlots: [
          { start: '08:00', end: '18:00' }
        ],
        allowedDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        includedRooms: ['room1', 'room2', 'room3'],
        canBookMeetingRooms: false,
        canAccessPremiumAreas: false,
        maxConsecutiveHours: 4,
        advanceBookingDays: 3
      },
      isActive: true,
      features: ['Accès espaces communs', 'WiFi gratuit', '4h consécutives max'],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: '2',
      name: 'Professionnel Mensuel',
      description: 'Abonnement mensuel pour professionnels',
      type: SubscriptionType.MONTHLY,
      membershipTypes: [MembershipType.PROFESSIONAL],
      price: 1200,
      currency: 'MAD ',
      duration: 30,
      rights: {
        maxReservationsPerDay: 5,
        maxReservationsPerWeek: 25,
        maxReservationsPerMonth: 100,
        allowedTimeSlots: [
          { start: '07:00', end: '22:00' }
        ],
        allowedDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
        includedRooms: ['room1', 'room2', 'room3', 'room4', 'meeting1'],
        canBookMeetingRooms: true,
        canAccessPremiumAreas: true,
        maxConsecutiveHours: 8,
        advanceBookingDays: 14
      },
      isActive: true,
      features: ['Accès illimité', 'Salles de réunion', 'Espaces premium', 'Support prioritaire'],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: '3',
      name: 'Flexible Premium',
      description: 'Formule flexible avec crédits',
      type: SubscriptionType.FLEXIBLE,
      membershipTypes: [MembershipType.PREMIUM, MembershipType.PROFESSIONAL], // Exemple de multi-sélection
      price: 800,
      currency: 'MAD ',
      duration: 30,
      rights: {
        maxReservationsPerDay: 3,
        maxReservationsPerWeek: 15,
        maxReservationsPerMonth: 50,
        allowedTimeSlots: [
          { start: '06:00', end: '23:00' }
        ],
        allowedDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
        includedRooms: ['room1', 'room2', 'room3', 'room4', 'room5', 'meeting1', 'meeting2'],
        canBookMeetingRooms: true,
        canAccessPremiumAreas: true,
        maxConsecutiveHours: 12,
        advanceBookingDays: 30
      },
      isActive: true,
      features: ['Accès 24/7', 'Toutes les salles', 'Réservation flexible', 'Conciergerie'],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    }
  ];

  // Données mockées pour les abonnements utilisateurs
  private mockUserSubscriptions: UserSubscription[] = [
    {
      id: '1',
      userId: '1',
      planId: '2',
      plan: this.mockPlans[1],
      status: SubscriptionStatus.ACTIVE,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-01-31'),
      autoRenew: true,
      paymentMethod: 'credit_card',
      lastPaymentDate: new Date('2024-01-01'),
      nextPaymentDate: new Date('2024-02-01'),
      remainingReservations: {
        daily: 3,
        weekly: 20,
        monthly: 85
      },
      usageStats: {
        totalReservations: 15,
        totalHours: 45,
        lastUsed: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    }
  ];

  // Signaux pour la réactivité
  private plansSignal = signal<SubscriptionPlan[]>(this.mockPlans);
  private userSubscriptionsSignal = signal<UserSubscription[]>(this.mockUserSubscriptions);

  constructor() {}

  // Gestion des plans d'abonnement
  getPlans(): Observable<SubscriptionPlan[]> {
    return of(this.plansSignal()).pipe(delay(300));
  }

  getPlanById(id: string): Observable<SubscriptionPlan | null> {
    const plan = this.plansSignal().find(p => p.id === id) || null;
    return of(plan).pipe(delay(200));
  }

  createPlan(request: CreateSubscriptionPlanRequest): Observable<SubscriptionPlan> {
    const newPlan: SubscriptionPlan = {
      id: Date.now().toString(),
      ...request,
      currency: 'MAD ',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentPlans = this.plansSignal();
    this.plansSignal.set([...currentPlans, newPlan]);

    return of(newPlan).pipe(delay(500));
  }

  updatePlan(id: string, request: UpdateSubscriptionPlanRequest): Observable<SubscriptionPlan> {
    const currentPlans = this.plansSignal();
    const planIndex = currentPlans.findIndex(p => p.id === id);

    if (planIndex === -1) {
      throw new Error('Plan non trouvé');
    }

    const updatedPlan = {
      ...currentPlans[planIndex],
      ...request,
      updatedAt: new Date()
    };

    const updatedPlans = [...currentPlans];
    updatedPlans[planIndex] = updatedPlan;
    this.plansSignal.set(updatedPlans);

    return of(updatedPlan).pipe(delay(500));
  }

  deletePlan(id: string): Observable<void> {
    const currentPlans = this.plansSignal();
    const filteredPlans = currentPlans.filter(p => p.id !== id);
    this.plansSignal.set(filteredPlans);

    return of(void 0).pipe(delay(300));
  }

  // Gestion des abonnements utilisateurs
  getUserSubscriptions(): Observable<UserSubscription[]> {
    return of(this.userSubscriptionsSignal()).pipe(delay(300));
  }

  getUserSubscriptionById(id: string): Observable<UserSubscription | null> {
    const subscription = this.userSubscriptionsSignal().find(s => s.id === id) || null;
    return of(subscription).pipe(delay(200));
  }

  assignSubscription(request: AssignSubscriptionRequest): Observable<UserSubscription> {
    const plan = this.plansSignal().find(p => p.id === request.planId);
    if (!plan) {
      throw new Error('Plan non trouvé');
    }

    const endDate = new Date(request.startDate);
    endDate.setDate(endDate.getDate() + plan.duration);

    const newSubscription: UserSubscription = {
      id: Date.now().toString(),
      userId: request.userId,
      planId: request.planId,
      plan: plan,
      status: SubscriptionStatus.ACTIVE,
      startDate: request.startDate,
      endDate: endDate,
      autoRenew: request.autoRenew,
      paymentMethod: request.paymentMethod,
      nextPaymentDate: endDate,
      remainingReservations: {
        daily: plan.rights.maxReservationsPerDay,
        weekly: plan.rights.maxReservationsPerWeek,
        monthly: plan.rights.maxReservationsPerMonth
      },
      usageStats: {
        totalReservations: 0,
        totalHours: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentSubscriptions = this.userSubscriptionsSignal();
    this.userSubscriptionsSignal.set([...currentSubscriptions, newSubscription]);

    return of(newSubscription).pipe(delay(500));
  }

  // Alertes et notifications
  getExpiringSubscriptions(days: number = 7): Observable<UserSubscription[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + days);

    return this.getUserSubscriptions().pipe(
      map(subscriptions =>
        subscriptions.filter(sub =>
          sub.status === SubscriptionStatus.ACTIVE &&
          sub.endDate <= cutoffDate
        )
      )
    );
  }

  getSubscriptionAlerts(): Observable<SubscriptionAlert[]> {
    // Mock des alertes
    const alerts: SubscriptionAlert[] = [
      {
        id: '1',
        userId: '1',
        subscriptionId: '1',
        type: 'expiration',
        message: 'Votre abonnement expire dans 3 jours',
        severity: 'warning',
        isRead: false,
        createdAt: new Date()
      }
    ];

    return of(alerts).pipe(delay(200));
  }

  // Statistiques
  getSubscriptionStats(): Observable<any> {
    const plans = this.plansSignal();
    const subscriptions = this.userSubscriptionsSignal();

    const stats = {
      totalPlans: plans.length,
      activePlans: plans.filter(p => p.isActive).length,
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter(s => s.status === SubscriptionStatus.ACTIVE).length,
      expiringSubscriptions: subscriptions.filter(s => {
        const daysUntilExpiry = Math.ceil((s.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
      }).length,
      monthlyRevenue: subscriptions
        .filter(s => s.status === SubscriptionStatus.ACTIVE)
        .reduce((sum, s) => sum + s.plan.price, 0)
    };

    return of(stats).pipe(delay(300));
  }
}
