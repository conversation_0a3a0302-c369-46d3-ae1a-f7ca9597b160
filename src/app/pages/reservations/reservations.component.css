/* Page container */
.reservations-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-button {
  height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  background: #6E56CF !important;
  border: none !important;
  color: white !important;
}

.add-button:hover {
  background: #5A45B8 !important;
  color: white !important;
}

/* Filters */
.filters-card {
  margin-bottom: 24px;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
  background: white;
}

.filters-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.filters-card .ant-card-body {
  padding: 24px !important;
}

.filters-row {
  display: flex;
  gap: 24px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.filter-item label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

/* FullCalendar section */
.calendar-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #E5E5EA;
  overflow: hidden;
  padding: 20px;
  position: relative;
}

/* Styles pour le scroll horizontal - désactivé par défaut sur tous les éléments */
.calendar-section ::ng-deep .fc,
.calendar-section ::ng-deep .fc-header-toolbar,
.calendar-section ::ng-deep .fc-view-harness,
.calendar-section ::ng-deep .fc-scroller,
.calendar-section ::ng-deep .fc-scroller-liquid-absolute {
  overflow-x: visible !important;
}

/* Légende du calendrier */
.calendar-legend {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-title {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

.legend-items {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
}

.legend-color.confirmed {
  background-color: #6E56CF;
}

.legend-color.pending {
  background-color: #B8A9E8;
}

.legend-color.cancelled {
  background-color: #C7C7CC;
}

/* FullCalendar customization */
.calendar-section ::ng-deep .fc {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.calendar-section ::ng-deep .fc-header-toolbar {
  margin-bottom: 20px;
  padding: 0 10px;
}

.calendar-section ::ng-deep .fc-button-primary {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-button-primary:hover {
  background-color: #5A45B8 !important;
  border-color: #5A45B8 !important;
}

.calendar-section ::ng-deep .fc-button-primary:disabled {
  background-color: #E5E5EA !important;
  border-color: #E5E5EA !important;
}

.calendar-section ::ng-deep .fc-today-button {
  background-color: #F0F0F0 !important;
  border-color: #E5E5EA !important;
  color: #1C1C1E !important;
}

.calendar-section ::ng-deep .fc-today-button:hover {
  background-color: #E5E5EA !important;
}

.calendar-section ::ng-deep .fc-daygrid-day {
  border-color: #F0F0F0 !important;
}

.calendar-section ::ng-deep .fc-daygrid-day-top {
  padding: 8px;
}

.calendar-section ::ng-deep .fc-day-today {
  background-color: #F0F5FF !important;
}

.calendar-section ::ng-deep .fc-event {
  border-radius: 8px !important;
  border: none !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  margin: 1px 2px !important;
  min-height: 20px !important;
  display: flex !important;
  align-items: center !important;
  opacity: 1 !important;
  background-color: inherit !important;
}

/* Forcer l'opacité sur tous les événements */
.calendar-section ::ng-deep .fc-event * {
  opacity: 1 !important;
}

.calendar-section ::ng-deep .fc-daygrid-event,
.calendar-section ::ng-deep .fc-timegrid-event {
  opacity: 1 !important;
  background-color: inherit !important;
}

/* Forcer les couleurs spécifiques selon le statut - MODE MOIS */
.calendar-section ::ng-deep .fc-daygrid-event[style*="background-color: rgb(110, 86, 207)"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#6E56CF"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#6e56cf"] {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="background-color: rgb(184, 169, 232)"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#B8A9E8"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#b8a9e8"] {
  background-color: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: #333 !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="background-color: rgb(199, 199, 204)"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#C7C7CC"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#c7c7cc"] {
  background-color: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Forcer les couleurs pour tous les événements confirmés */
.calendar-section ::ng-deep .fc-event.confirmed-event {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-event.pending-event {
  background-color: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: #333 !important;
}

.calendar-section ::ng-deep .fc-event.cancelled-event {
  background-color: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Forcer les couleurs sur les éléments internes */
.calendar-section ::ng-deep .fc-daygrid-event .fc-event-main,
.calendar-section ::ng-deep .fc-timegrid-event .fc-event-main {
  background-color: inherit !important;
  color: inherit !important;
}

.calendar-section ::ng-deep .fc-event-title {
  color: inherit !important;
}

/* SOLUTION CORRIGÉE - Couleurs selon le statut par défaut */
.calendar-section ::ng-deep .fc-daygrid-event {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event .fc-event-main {
  background: inherit !important;
  color: inherit !important;
}

/* Forcer selon les classes de statut */
.calendar-section ::ng-deep .fc-daygrid-event[class*="confirmed"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[class*="pending"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[class*="cancelled"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Forcer les couleurs selon le style inline pour le mode Mois */
.calendar-section ::ng-deep .fc-daygrid-event[style*="#6E56CF"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#6e56cf"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="rgb(110, 86, 207)"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="#6E56CF"] .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event[style*="#6e56cf"] .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event[style*="rgb(110, 86, 207)"] .fc-event-title {
  background: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="#B8A9E8"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#b8a9e8"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="rgb(184, 169, 232)"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="#B8A9E8"] .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event[style*="#b8a9e8"] .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event[style*="rgb(184, 169, 232)"] .fc-event-title {
  background: #B8A9E8 !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="#C7C7CC"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="#c7c7cc"],
.calendar-section ::ng-deep .fc-daygrid-event[style*="rgb(199, 199, 204)"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

.calendar-section ::ng-deep .fc-daygrid-event[style*="#C7C7CC"] .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event[style*="#c7c7cc"] .fc-event-title,
.calendar-section ::ng-deep .fc-daygrid-event[style*="rgb(199, 199, 204)"] .fc-event-title {
  background: #C7C7CC !important;
  color: #333 !important;
}

/* STYLES OPTIMISÉS POUR LES MODES JOUR ET SEMAINE */

/* Mode Jour et Semaine - Événements timeGrid */
.calendar-section ::ng-deep .fc-timegrid-event {
  border-radius: 6px !important;
  border: none !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  margin: 1px !important;
  min-height: 20px !important;
  opacity: 1 !important;
}

/* Couleurs pour le mode Jour et Semaine selon le statut */
.calendar-section ::ng-deep .fc-timegrid-event[style*="#6E56CF"],
.calendar-section ::ng-deep .fc-timegrid-event[style*="#6e56cf"],
.calendar-section ::ng-deep .fc-timegrid-event[style*="rgb(110, 86, 207)"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-timegrid-event[style*="#B8A9E8"],
.calendar-section ::ng-deep .fc-timegrid-event[style*="#b8a9e8"],
.calendar-section ::ng-deep .fc-timegrid-event[style*="rgb(184, 169, 232)"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-timegrid-event[style*="#C7C7CC"],
.calendar-section ::ng-deep .fc-timegrid-event[style*="#c7c7cc"],
.calendar-section ::ng-deep .fc-timegrid-event[style*="rgb(199, 199, 204)"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Couleurs par classe pour le mode Jour et Semaine */
.calendar-section ::ng-deep .fc-timegrid-event[class*="confirmed"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-timegrid-event[class*="pending"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-timegrid-event[class*="cancelled"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Titres des événements dans les modes Jour et Semaine */
.calendar-section ::ng-deep .fc-timegrid-event .fc-event-title,
.calendar-section ::ng-deep .fc-timegrid-event .fc-event-main {
  background: inherit !important;
  color: inherit !important;
  font-weight: 500 !important;
}

.calendar-section ::ng-deep .fc-event:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  filter: brightness(1.1) !important;
}

/* Supprimer complètement les dots et anciens styles des événements */
.calendar-section ::ng-deep .fc-daygrid-event-dot {
  display: none !important;
}

.calendar-section ::ng-deep .fc-event-dot {
  display: none !important;
}

/* Supprimer les anciens styles de points dans le mode Planning */
.calendar-section ::ng-deep .fc-list-event-dot {
  display: none !important;
}

.calendar-section ::ng-deep .fc-list-event-graphic {
  display: none !important;
}

.calendar-section ::ng-deep .fc-list-event:before {
  display: none !important;
}

/* Forcer le style rectangulaire pour tous les modes */
.calendar-section ::ng-deep .fc-event,
.calendar-section ::ng-deep .fc-daygrid-event,
.calendar-section ::ng-deep .fc-timegrid-event,
.calendar-section ::ng-deep .fc-list-event {
  border-radius: 8px !important;
  border: none !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  margin: 1px 2px !important;
  min-height: 20px !important;
  display: flex !important;
  align-items: center !important;
  background: none !important;
}

/* Supprimer tous les éléments graphiques par défaut */
.calendar-section ::ng-deep .fc-list-event .fc-list-event-graphic,
.calendar-section ::ng-deep .fc-list-event .fc-list-event-dot {
  display: none !important;
}

/* Styles spécifiques pour le mode Planning (liste) */
.calendar-section ::ng-deep .fc-list-event {
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  border-bottom: 1px solid #f0f0f0 !important;
  background: white !important;
  padding: 12px 16px !important;
}

.calendar-section ::ng-deep .fc-list-event-title {
  background: linear-gradient(135deg, #6E56CF, #8B7ED8) !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  margin: 0 !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(110, 86, 207, 0.2) !important;
}

.calendar-section ::ng-deep .fc-list-event-time {
  color: #666 !important;
  font-weight: 600 !important;
  margin-right: 20px !important;
  min-width: 80px !important;
  display: inline-block !important;
  text-align: right !important;
}

/* Style pour les événements dans la vue mensuelle */
.calendar-section ::ng-deep .fc-daygrid-event {
  border-radius: 6px !important;
  margin: 1px 0 !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Style pour les événements dans la vue semaine/jour */
.calendar-section ::ng-deep .fc-timegrid-event {
  border-radius: 6px !important;
  border: none !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Améliorer le texte des événements */
.calendar-section ::ng-deep .fc-event-title {
  font-weight: 500 !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.calendar-section ::ng-deep .fc-event-time {
  font-weight: 400 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 10px !important;
}

/* Couleurs spécifiques par statut - Force l'application des styles */
::ng-deep .fc-event {
  opacity: 1 !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
  border: 2px solid !important;
}

/* Confirmé - Violet principal */
::ng-deep .fc-event.confirmed-event,
::ng-deep .fc-event[style*="#6E56CF"],
::ng-deep .fc-event[style*="#6e56cf"],
::ng-deep .fc-event[style*="rgb(110, 86, 207)"] {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

/* En attente - Violet soft */
::ng-deep .fc-event.pending-event,
::ng-deep .fc-event[style*="#B8A9E8"],
::ng-deep .fc-event[style*="#b8a9e8"],
::ng-deep .fc-event[style*="rgb(184, 169, 232)"] {
  background-color: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

/* Annulé - Gris clair */
::ng-deep .fc-event.cancelled-event,
::ng-deep .fc-event[style*="#C7C7CC"],
::ng-deep .fc-event[style*="#c7c7cc"],
::ng-deep .fc-event[style*="rgb(199, 199, 204)"] {
  background-color: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Style par défaut */
::ng-deep .fc-event.default-event {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: white !important;
}

/* Annulé - Rouge solide */
.calendar-section ::ng-deep .fc-event[style*="rgb(239, 68, 68)"],
.calendar-section ::ng-deep .fc-event[style*="#ef4444"] {
  background: #ef4444 !important;
  border: 1px solid #dc2626 !important;
  color: white !important;
  opacity: 1 !important;
}

/* Principal - Violet solide */
.calendar-section ::ng-deep .fc-event[style*="rgb(110, 86, 207)"],
.calendar-section ::ng-deep .fc-event[style*="#6E56CF"] {
  background: #6E56CF !important;
  border: 1px solid #5A45B8 !important;
  color: white !important;
  opacity: 1 !important;
}

/* Animation de transition */
.calendar-section ::ng-deep .fc-event {
  transition: all 0.2s ease !important;
}

/* Améliorer la lisibilité sur les petits événements */
.calendar-section ::ng-deep .fc-event-main {
  padding: 0 !important;
  opacity: 1 !important;
}

.calendar-section ::ng-deep .fc-event-main-frame {
  padding: 0 !important;
  opacity: 1 !important;
}

/* Forcer l'opacité sur tous les éléments des événements */
.calendar-section ::ng-deep .fc-event * {
  opacity: 1 !important;
}

.calendar-section ::ng-deep .fc-daygrid-event,
.calendar-section ::ng-deep .fc-timegrid-event {
  opacity: 1 !important;
}

/* Styles spécifiques pour le contenu des événements */
.calendar-section ::ng-deep .fc-event-title,
.calendar-section ::ng-deep .fc-event-time {
  opacity: 1 !important;
  color: white !important;
}

.calendar-section ::ng-deep .fc-timegrid-slot {
  height: 40px !important;
}

.calendar-section ::ng-deep .fc-timegrid-axis {
  width: 80px !important;
}

.calendar-section ::ng-deep .fc-col-header-cell {
  background-color: #FAFAFA !important;
  border-color: #E5E5EA !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.calendar-section ::ng-deep .fc-scrollgrid {
  border-color: #E5E5EA !important;
}

.calendar-section ::ng-deep .fc-theme-standard td,
.calendar-section ::ng-deep .fc-theme-standard th {
  border-color: #F0F0F0 !important;
}

/* Calendar cells */
.calendar-cell {
  height: 100px;
  overflow: hidden;
}

.cell-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.date-number {
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4px;
}

.reservations-list {
  flex: 1;
  overflow: hidden;
}

.reservation-item {
  background: #F0F0F0;
  border-radius: 4px;
  padding: 2px 6px;
  margin-bottom: 2px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reservation-item:hover {
  background: #E0E0E0;
}

.reservation-item.status-confirmed {
  background: #E6F7E6;
  color: #52C41A;
}

.reservation-item.status-pending {
  background: #FFF7E6;
  color: #FA8C16;
}

.reservation-item.status-cancelled {
  background: #FFE6E6;
  color: #FF4D4F;
}

.reservation-time {
  font-weight: 600;
}

.reservation-space,
.reservation-user {
  font-size: 10px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Month cells */
.month-cell {
  text-align: center;
  padding: 8px;
}

.reservations-count {
  background: #6E56CF;
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
}

/* Daily reservations */
.daily-reservations {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.daily-reservations .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.daily-reservations .ant-card-body {
  padding: 24px !important;
}

.reservations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.reservation-card {
  background: white;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.reservation-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.reservation-card.status-confirmed {
  border-left: 4px solid #52C41A;
}

.reservation-card.status-pending {
  border-left: 4px solid #FA8C16;
}

.reservation-card.status-cancelled {
  border-left: 4px solid #FF4D4F;
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reservation-title {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
}

.reservation-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #8E8E93;
}

.detail-item nz-icon {
  color: #6E56CF;
}

.reservation-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  border-top: 1px solid #F0F0F0;
  padding-top: 12px;
}

/* Responsive */
@media (max-width: 1200px) {
  .reservations-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

/* Responsive pour tablettes */
@media (max-width: 992px) {
  .reservations-container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 26px;
  }

  .filters-row {
    flex-wrap: wrap;
    gap: 16px;
  }

  .filter-item {
    min-width: 180px;
  }

  .calendar-section {
    padding: 16px;
  }

  .legend-items {
    flex-wrap: wrap;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .reservations-container {
    padding: 0 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    text-align: left;
    padding: 16px 0;
  }

  .header-content {
    text-align: left;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
    flex-direction: column;
    gap: 12px;
  }

  .add-button {
    width: 100% !important;
    justify-content: center !important;
    height: 48px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
  }

  /* Filtres responsive */
  .filters-card {
    margin-bottom: 16px;
  }

  .filters-card .ant-card-body {
    padding: 16px !important;
  }

  .filters-row {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-item {
    min-width: auto;
    flex: none;
    width: 100%;
  }

  .filter-item label {
    font-size: 14px;
    margin-bottom: 6px;
    display: block;
  }

  /* Calendrier responsive */
  .calendar-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .calendar-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
  }

  .legend-items {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
    width: 100%;
  }

  .legend-item {
    font-size: 12px;
  }

  /* FullCalendar mobile optimizations */
  .calendar-section ::ng-deep .fc-header-toolbar {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  .calendar-section ::ng-deep .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .calendar-section ::ng-deep .fc-button {
    font-size: 12px !important;
    padding: 6px 12px !important;
    height: auto !important;
  }

  .calendar-section ::ng-deep .fc-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  /* Scroll horizontal UNIQUEMENT sur la grille du calendrier (pas la toolbar) */
  .calendar-section ::ng-deep .fc-view-harness {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #E5E5EA transparent;
  }

  .calendar-section ::ng-deep .fc-view-harness::-webkit-scrollbar {
    height: 8px;
  }

  .calendar-section ::ng-deep .fc-view-harness::-webkit-scrollbar-track {
    background: #F8F9FA;
    border-radius: 4px;
  }

  .calendar-section ::ng-deep .fc-view-harness::-webkit-scrollbar-thumb {
    background: #E5E5EA;
    border-radius: 4px;
  }

  .calendar-section ::ng-deep .fc-view-harness::-webkit-scrollbar-thumb:hover {
    background: #D1D1D6;
  }

  /* S'assurer que les autres éléments ne scrollent pas */
  .calendar-section,
  .calendar-section ::ng-deep .fc,
  .calendar-section ::ng-deep .fc-header-toolbar {
    overflow-x: visible !important;
  }

  .calendar-section ::ng-deep .fc-daygrid-body,
  .calendar-section ::ng-deep .fc-col-header,
  .calendar-section ::ng-deep .fc-scrollgrid,
  .calendar-section ::ng-deep .fc-scrollgrid-sync-table {
    min-width: 700px !important; /* Force une largeur minimale pour 7 jours */
  }

  .calendar-section ::ng-deep .fc-col-header-cell,
  .calendar-section ::ng-deep .fc-daygrid-day {
    min-width: 100px !important; /* Largeur minimale par jour/case */
    width: 100px !important; /* Force la largeur exacte */
    max-width: 100px !important;
  }

  .calendar-section ::ng-deep .fc-col-header-cell {
    font-size: 12px;
    padding: 8px 4px;
  }

  .calendar-section ::ng-deep .fc-daygrid-day {
    min-height: 80px !important;
  }

  /* Force la largeur sur tous les éléments de la grille */
  .calendar-section ::ng-deep .fc-daygrid-day-frame,
  .calendar-section ::ng-deep .fc-daygrid-day-top,
  .calendar-section ::ng-deep .fc-daygrid-day-events,
  .calendar-section ::ng-deep .fc-daygrid-day-bg {
    width: 100% !important;
  }

  .calendar-section ::ng-deep .fc-daygrid-day-number {
    font-size: 12px;
    padding: 4px;
  }

  .calendar-section ::ng-deep .fc-event {
    font-size: 10px !important;
    padding: 2px 4px !important;
    margin: 1px 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .calendar-section ::ng-deep .fc-timegrid-slot {
    height: 30px !important;
  }

  .calendar-section ::ng-deep .fc-timegrid-axis {
    width: 60px !important;
    font-size: 11px;
  }



  .calendar-cell {
    height: 80px;
  }

  .reservations-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .reservation-card {
    padding: 12px;
  }

  .reservation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .reservation-title {
    font-size: 14px;
  }

  .detail-item {
    font-size: 12px;
  }

  .reservation-actions {
    flex-direction: column;
    gap: 8px;
  }

  .reservation-actions button {
    width: 100%;
    justify-content: center;
  }
}

/* Responsive pour très petits écrans */
@media (max-width: 480px) {
  .reservations-container {
    padding: 0 8px;
  }

  .page-header {
    padding: 12px 0;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .filters-card .ant-card-body {
    padding: 12px !important;
  }

  .calendar-section {
    padding: 8px;
    overflow-x: visible !important;
  }

  /* Scroll uniquement sur la grille du calendrier pour très petits écrans */
  .calendar-section ::ng-deep .fc-view-harness {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  /* S'assurer que la toolbar ne scroll pas */
  .calendar-section ::ng-deep .fc,
  .calendar-section ::ng-deep .fc-header-toolbar {
    overflow-x: visible !important;
  }

  .calendar-legend {
    padding: 8px;
  }

  .legend-items {
    gap: 8px;
  }

  .legend-item {
    font-size: 11px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
  }

  /* FullCalendar très petit écran avec scroll horizontal */
  .calendar-section ::ng-deep .fc-button {
    font-size: 11px !important;
    padding: 4px 8px !important;
  }

  .calendar-section ::ng-deep .fc-toolbar-title {
    font-size: 16px !important;
  }

  /* Maintenir une largeur minimale même sur très petits écrans */
  .calendar-section ::ng-deep .fc-daygrid-body,
  .calendar-section ::ng-deep .fc-col-header,
  .calendar-section ::ng-deep .fc-scrollgrid,
  .calendar-section ::ng-deep .fc-scrollgrid-sync-table {
    min-width: 595px !important; /* 7 jours × 85px = 595px */
  }

  .calendar-section ::ng-deep .fc-col-header-cell,
  .calendar-section ::ng-deep .fc-daygrid-day {
    min-width: 85px !important; /* Largeur minimale réduite mais lisible */
    width: 85px !important; /* Force la largeur exacte */
    max-width: 85px !important;
  }

  .calendar-section ::ng-deep .fc-col-header-cell {
    font-size: 11px;
    padding: 6px 2px;
  }

  .calendar-section ::ng-deep .fc-daygrid-day {
    min-height: 70px !important;
  }

  /* Force la largeur sur tous les éléments de la grille pour petits écrans */
  .calendar-section ::ng-deep .fc-daygrid-day-frame,
  .calendar-section ::ng-deep .fc-daygrid-day-top,
  .calendar-section ::ng-deep .fc-daygrid-day-events,
  .calendar-section ::ng-deep .fc-daygrid-day-bg {
    width: 100% !important;
  }

  .calendar-section ::ng-deep .fc-daygrid-day-number {
    font-size: 11px;
    padding: 2px;
  }

  .calendar-section ::ng-deep .fc-event {
    font-size: 9px !important;
    padding: 1px 3px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .calendar-section ::ng-deep .fc-timegrid-axis {
    width: 50px !important;
    font-size: 10px;
  }

  /* Améliorer l'indicateur de scroll */
  .calendar-section::before {
    content: "← Faites défiler horizontalement pour voir plus de jours →";
    display: block;
    text-align: center;
    font-size: 11px;
    color: #8E8E93;
    padding: 8px;
    background: #F8F9FA;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  .reservation-card {
    padding: 10px;
  }

  .reservation-title {
    font-size: 13px;
  }

  .detail-item {
    font-size: 11px;
  }
}

/* Week view */
.week-view,
.day-view {
  margin-bottom: 24px;
}

.week-card,
.day-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.week-header,
.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #E5E5EA;
  margin-bottom: 20px;
}

.week-title,
.day-title {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
}

.week-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #E5E5EA;
  border-radius: 8px;
  overflow: hidden;
}

.week-day {
  background: white;
  min-height: 200px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.week-day:hover {
  background: #F8F9FA;
}

.week-day.selected {
  background: #F0F5FF;
  border: 2px solid #6E56CF;
}

.day-header {
  padding: 12px 16px;
  border-bottom: 1px solid #E5E5EA;
  background: #FAFAFA;
}

.day-name {
  font-size: 12px;
  font-weight: 600;
  color: #8E8E93;
  text-transform: uppercase;
}

.day-number {
  font-size: 18px;
  font-weight: 700;
  color: #1C1C1E;
  margin-top: 4px;
}

.day-reservations {
  padding: 8px;
}

/* Day view timeline */
.day-timeline {
  max-height: 600px;
  overflow-y: auto;
}

.timeline-hours {
  display: flex;
  flex-direction: column;
}

.hour-slot {
  display: flex;
  min-height: 60px;
  border-bottom: 1px solid #F0F0F0;
}

.hour-label {
  width: 80px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #8E8E93;
  background: #FAFAFA;
  border-right: 1px solid #E5E5EA;
  display: flex;
  align-items: flex-start;
}

.hour-reservations {
  flex: 1;
  position: relative;
  padding: 4px;
}

.reservation-block {
  position: relative;
  background: #E6F7E6;
  border: 1px solid #52C41A;
  border-radius: 4px;
  padding: 8px;
  margin: 2px 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reservation-block:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reservation-block.status-pending {
  background: #FFF7E6;
  border-color: #FA8C16;
}

.reservation-block.status-cancelled {
  background: #FFE6E6;
  border-color: #FF4D4F;
}

.reservation-block .reservation-title {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4px;
}

.reservation-block .reservation-time {
  font-size: 12px;
  color: #8E8E93;
  margin-bottom: 2px;
}

.reservation-block .reservation-user {
  font-size: 11px;
  color: #8E8E93;
}

/* Responsive pour les nouvelles vues */
@media (max-width: 768px) {
  .week-days {
    grid-template-columns: 1fr;
  }

  .week-day {
    min-height: 150px;
  }

  .hour-label {
    width: 60px;
    font-size: 11px;
  }

  .reservation-block {
    padding: 6px;
  }

  .reservation-block .reservation-title {
    font-size: 12px;
  }
}
