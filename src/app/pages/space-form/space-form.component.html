<div class="space-form-page">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-info">
        <h1 class="page-title">
          <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
          {{ isEditMode ? 'Modifier l\'espace' : 'Nouvel espace' }}
        </h1>
        <p class="page-description">
          {{ isEditMode ? 'Modifiez les informations de l\'espace' : 'Créez un nouvel espace de travail' }}
        </p>
      </div>
      <div class="header-actions">
        <button nz-button nzType="default" (click)="goBack()">
          <nz-icon nzType="arrow-left" nzTheme="outline"></nz-icon>
          <span>Retour</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Contenu du formulaire -->
  <div class="form-content">
    <nz-spin [nzSpinning]="loading" nzTip="Chargement...">
      <form nz-form [formGroup]="spaceForm" (ngSubmit)="onSubmit()" *ngIf="!loading">

        <!-- Informations de base -->
        <nz-card nzTitle="Informations générales" class="form-card">
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Nom de l'espace</nz-form-label>
                <nz-form-control nzErrorTip="Le nom est requis (min. 2 caractères)">
                  <input nz-input formControlName="name" placeholder="Ex: Salle de réunion Alpha" />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Type d'espace</nz-form-label>
                <nz-form-control nzErrorTip="Le type est requis">
                  <nz-select formControlName="type" nzPlaceHolder="Sélectionner un type">
                    <nz-option
                      *ngFor="let option of spaceTypeOptions"
                      [nzLabel]="option.label"
                      [nzValue]="option.value"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="24">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Description</nz-form-label>
                <nz-form-control nzErrorTip="La description est requise (min. 10 caractères)">
                  <textarea
                    nz-input
                    formControlName="description"
                    placeholder="Décrivez l'espace, ses caractéristiques..."
                    rows="3"
                  ></textarea>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Capacité</nz-form-label>
                <nz-form-control nzErrorTip="La capacité doit être d'au moins 1 personne">
                  <nz-input-number
                    formControlName="capacity"
                    [nzMin]="1"
                    [nzMax]="100"
                    nzPlaceHolder="Nombre de personnes"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Superficie (m²)</nz-form-label>
                <nz-form-control nzErrorTip="La superficie doit être d'au moins 1 m²">
                  <nz-input-number
                    formControlName="area"
                    [nzMin]="1"
                    [nzMax]="1000"
                    nzPlaceHolder="Surface en m²"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Localisation</nz-form-label>
                <nz-form-control nzErrorTip="La localisation est requise">
                  <input nz-input formControlName="location" placeholder="Ex: Zone A, Bâtiment 1" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label>Étage</nz-form-label>
                <nz-form-control>
                  <input nz-input formControlName="floor" placeholder="Ex: RDC, 1er étage" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </nz-card>

        <!-- Équipements -->
        <nz-card class="form-card">
          <div slot="title" class="card-title-with-action">
            <span>Équipements</span>
            <button nz-button nzType="dashed" nzSize="small" (click)="addEquipment()">
              <nz-icon nzType="plus" nzTheme="outline"></nz-icon>
              <span>Ajouter</span>
            </button>
          </div>

          <div formArrayName="equipment">
            <div
              *ngFor="let equipment of equipmentArray.controls; let i = index"
              [formGroupName]="i"
              class="equipment-item"
            >
              <div class="equipment-header">
                <h4>Équipement {{ i + 1 }}</h4>
                <button
                  nz-button
                  nzType="text"
                  nzDanger
                  nzSize="small"
                  (click)="removeEquipment(i)"
                  *ngIf="equipmentArray.length > 0"
                >
                  <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                </button>
              </div>

              <div nz-row [nzGutter]="16">
                <div nz-col nzSpan="8">
                  <nz-form-item>
                    <nz-form-label [nzRequired]="true">Nom</nz-form-label>
                    <nz-form-control nzErrorTip="Le nom est requis">
                      <input nz-input formControlName="name" placeholder="Ex: Écran 24 pouces" />
                    </nz-form-control>
                  </nz-form-item>
                </div>
                <div nz-col nzSpan="8">
                  <nz-form-item>
                    <nz-form-label [nzRequired]="true">Type</nz-form-label>
                    <nz-form-control nzErrorTip="Le type est requis">
                      <nz-select formControlName="type" nzPlaceHolder="Type d'équipement">
                        <nz-option
                          *ngFor="let option of equipmentTypeOptions"
                          [nzLabel]="option.label"
                          [nzValue]="option.value"
                        ></nz-option>
                      </nz-select>
                    </nz-form-control>
                  </nz-form-item>
                </div>
                <div nz-col nzSpan="8">
                  <nz-form-item>
                    <nz-form-label [nzRequired]="true">Quantité</nz-form-label>
                    <nz-form-control nzErrorTip="La quantité doit être d'au moins 1">
                      <nz-input-number
                        formControlName="quantity"
                        [nzMin]="1"
                        [nzMax]="50"
                        style="width: 100%"
                      ></nz-input-number>
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>

              <div nz-row [nzGutter]="16">
                <div nz-col nzSpan="8">
                  <nz-form-item>
                    <nz-form-label>Marque</nz-form-label>
                    <nz-form-control>
                      <input nz-input formControlName="brand" placeholder="Ex: Dell, HP..." />
                    </nz-form-control>
                  </nz-form-item>
                </div>
                <div nz-col nzSpan="8">
                  <nz-form-item>
                    <nz-form-label>Modèle</nz-form-label>
                    <nz-form-control>
                      <input nz-input formControlName="model" placeholder="Ex: U2419H" />
                    </nz-form-control>
                  </nz-form-item>
                </div>
                <div nz-col nzSpan="8">
                  <nz-form-item>
                    <nz-form-label>Statut</nz-form-label>
                    <nz-form-control>
                      <nz-select formControlName="status">
                        <nz-option nzLabel="Fonctionnel" [nzValue]="EquipmentStatus.WORKING"></nz-option>
                        <nz-option nzLabel="En panne" [nzValue]="EquipmentStatus.BROKEN"></nz-option>
                        <nz-option nzLabel="Maintenance" [nzValue]="EquipmentStatus.MAINTENANCE"></nz-option>
                      </nz-select>
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>

              <div nz-row [nzGutter]="16">
                <div nz-col nzSpan="24">
                  <nz-form-item>
                    <nz-form-label>Description</nz-form-label>
                    <nz-form-control>
                      <textarea
                        nz-input
                        formControlName="description"
                        placeholder="Description détaillée de l'équipement..."
                        rows="2"
                      ></textarea>
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>

              <nz-divider *ngIf="i < equipmentArray.length - 1"></nz-divider>
            </div>

            <div *ngIf="equipmentArray.length === 0" class="empty-state">
              <nz-empty
                nzNotFoundImage="simple"
                nzNotFoundContent="Aucun équipement ajouté"
              >
                <div nz-empty-footer>
                  <button nz-button nzType="primary" (click)="addEquipment()">
                    <nz-icon nzType="plus" nzTheme="outline"></nz-icon>
                    <span>Ajouter un équipement</span>
                  </button>
                </div>
              </nz-empty>
            </div>
          </div>
        </nz-card>

        <!-- Commodités -->
        <nz-card nzTitle="Commodités et services" class="form-card">
          <nz-form-item>
            <nz-form-label>Commodités disponibles</nz-form-label>
            <nz-form-control>
              <nz-select
                formControlName="amenities"
                nzMode="multiple"
                nzPlaceHolder="Sélectionner les commodités"
                style="width: 100%"
              >
                <nz-option
                  *ngFor="let amenity of availableAmenities"
                  [nzLabel]="amenity"
                  [nzValue]="amenity"
                ></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </nz-card>

        <!-- Tarification -->
        <nz-card nzTitle="Tarification" class="form-card" formGroupName="pricing">
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Prix horaire</nz-form-label>
                <nz-form-control nzErrorTip="Le prix horaire est requis">
                  <nz-input-number
                    formControlName="hourlyRate"
                    [nzMin]="0"
                    [nzStep]="0.5"
                    nzPlaceHolder="0.00"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Prix journalier</nz-form-label>
                <nz-form-control nzErrorTip="Le prix journalier est requis">
                  <nz-input-number
                    formControlName="dailyRate"
                    [nzMin]="0"
                    [nzStep]="1"
                    nzPlaceHolder="0.00"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Prix hebdomadaire</nz-form-label>
                <nz-form-control nzErrorTip="Le prix hebdomadaire est requis">
                  <nz-input-number
                    formControlName="weeklyRate"
                    [nzMin]="0"
                    [nzStep]="5"
                    nzPlaceHolder="0.00"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label [nzRequired]="true">Prix mensuel</nz-form-label>
                <nz-form-control nzErrorTip="Le prix mensuel est requis">
                  <nz-input-number
                    formControlName="monthlyRate"
                    [nzMin]="0"
                    [nzStep]="10"
                    nzPlaceHolder="0.00"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </nz-card>

        <!-- Disponibilités -->
        <nz-card nzTitle="Disponibilités" class="form-card" formGroupName="availability">
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label>Espace actif</nz-form-label>
                <nz-form-control>
                  <nz-switch formControlName="isActive"></nz-switch>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label>Réservation à l'avance (jours)</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="advanceBookingDays"
                    [nzMin]="1"
                    [nzMax]="365"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label>Durée min. (minutes)</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="minBookingDuration"
                    [nzMin]="15"
                    [nzStep]="15"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label>Durée max. (minutes)</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="maxBookingDuration"
                    [nzMin]="60"
                    [nzStep]="30"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <!-- Horaires hebdomadaires -->
          <nz-divider nzText="Horaires d'ouverture"></nz-divider>
          <div formGroupName="schedule">
            <div
              *ngFor="let day of ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']"
              [formGroupName]="day"
              class="day-schedule"
            >
              <div class="day-header">
                <nz-form-item>
                  <nz-form-control>
                    <label nz-checkbox formControlName="isOpen">
                      {{ getDayLabel(day) }}
                    </label>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div class="day-times" *ngIf="spaceForm.get('availability.schedule.' + day + '.isOpen')?.value">
                <nz-form-item>
                  <nz-form-label>Ouverture</nz-form-label>
                  <nz-form-control>
                    <nz-time-picker
                      formControlName="openTime"
                      nzFormat="HH:mm"
                      style="width: 100%"
                    ></nz-time-picker>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label>Fermeture</nz-form-label>
                  <nz-form-control>
                    <nz-time-picker
                      formControlName="closeTime"
                      nzFormat="HH:mm"
                      style="width: 100%"
                    ></nz-time-picker>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>
        </nz-card>

        <!-- Règles -->
        <nz-card class="form-card">
          <div slot="title" class="card-title-with-action">
            <span>Règles d'utilisation</span>
            <button nz-button nzType="dashed" nzSize="small" (click)="addRule()">
              <nz-icon nzType="plus" nzTheme="outline"></nz-icon>
              <span>Ajouter</span>
            </button>
          </div>

          <div formArrayName="rules">
            <div *ngFor="let rule of rulesArray.controls; let i = index" class="rule-item">
              <nz-form-item>
                <nz-form-control nzErrorTip="La règle est requise">
                  <nz-input-group [nzSuffix]="deleteIcon">
                    <input
                      nz-input
                      [formControlName]="i"
                      placeholder="Ex: Maintenir l'espace propre"
                    />
                    <ng-template #deleteIcon>
                      <button
                        nz-button
                        nzType="text"
                        nzDanger
                        nzSize="small"
                        (click)="removeRule(i)"
                        *ngIf="rulesArray.length > 0"
                      >
                        <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                      </button>
                    </ng-template>
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>

            <div *ngIf="rulesArray.length === 0" class="empty-state">
              <nz-empty
                nzNotFoundImage="simple"
                nzNotFoundContent="Aucune règle définie"
              >
                <div nz-empty-footer>
                  <button nz-button nzType="primary" (click)="addRule()">
                    <nz-icon nzType="plus" nzTheme="outline"></nz-icon>
                    <span>Ajouter une règle</span>
                  </button>
                </div>
              </nz-empty>
            </div>
          </div>
        </nz-card>

        <!-- Actions -->
        <div class="form-actions">
          <button nz-button nzType="default" (click)="goBack()">
            <span>Annuler</span>
          </button>
          <button
            nz-button
            nzType="primary"
            nzHtmlType="submit"
            [nzLoading]="saving"
            [disabled]="spaceForm.invalid"
          >
            <nz-icon nzType="save" nzTheme="outline"></nz-icon>
            <span>{{ isEditMode ? 'Modifier' : 'Créer' }}</span>
          </button>
        </div>

      </form>
    </nz-spin>
  </div>
</div>
