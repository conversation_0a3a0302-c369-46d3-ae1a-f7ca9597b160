<div class="subscription-details-page">
  <!-- Contenu principal -->
  <div class="page-content" *ngIf="!loading; else loadingTemplate">
    <div class="subscription-details-container" *ngIf="plan">
      <!-- En-tête -->
      <div class="details-header">
        <div class="plan-info">
          <div class="plan-icon">
            <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
          </div>
          <div class="plan-basic-info">
            <h1 class="plan-name">{{ plan.name }}</h1>
            <p class="plan-description">{{ plan.description }}</p>
            <div class="plan-badges">
              <nz-tag [nzColor]="getTypeColor(plan.type)">
                {{ getTypeLabel(plan.type) }}
              </nz-tag>
              <nz-tag
                *ngFor="let membershipType of plan.membershipTypes"
                [nzColor]="getMembershipColor(membershipType)"
                style="margin-right: 4px;"
              >
                {{ getMembershipLabel(membershipType) }}
              </nz-tag>
              <nz-tag [nzColor]="plan.isActive ? 'success' : 'default'">
                {{ plan.isActive ? 'Actif' : 'Inactif' }}
              </nz-tag>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button nz-button nzType="default" (click)="goBack()">
            <nz-icon nzType="arrow-left" nzTheme="outline"></nz-icon>
            <span>Retour</span>
          </button>
          <button nz-button nzType="primary" (click)="editPlan()">
            <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
            <span>Modifier</span>
          </button>
        </div>
      </div>

      <!-- Contenu des détails -->
      <div class="details-content">
        <!-- Informations générales -->
        <nz-card nzTitle="Informations générales" class="info-card">
          <div class="subscription-info-grid">
            <div class="info-item">
              <div class="info-label">Prix</div>
              <div class="info-value">
                <span class="price-value">{{ plan.price }} {{ plan.currency }}</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">Durée</div>
              <div class="info-value">{{ getDurationText(plan) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">Type</div>
              <div class="info-value">
                <nz-tag [nzColor]="getTypeColor(plan.type)">
                  {{ getTypeLabel(plan.type) }}
                </nz-tag>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">Catégories</div>
              <div class="info-value">
                <nz-tag
                  *ngFor="let membershipType of plan.membershipTypes"
                  [nzColor]="getMembershipColor(membershipType)"
                  style="margin-right: 4px;"
                >
                  {{ getMembershipLabel(membershipType) }}
                </nz-tag>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">Statut</div>
              <div class="info-value">
                <nz-tag [nzColor]="plan.isActive ? 'success' : 'default'">
                  {{ plan.isActive ? 'Actif' : 'Inactif' }}
                </nz-tag>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">Date de création</div>
              <div class="info-value">{{ plan.createdAt | date:'dd/MM/yyyy HH:mm' }}</div>
            </div>
          </div>
        </nz-card>

        <!-- Droits et restrictions -->
        <nz-card nzTitle="Droits et restrictions" class="info-card">
          <div class="rights-grid">
            <div class="rights-section">
              <h4>Limites de réservation</h4>
              <div class="rights-list">
                <div class="rights-item">
                  <span class="rights-label">Par jour :</span>
                  <span class="rights-value">{{ plan.rights.maxReservationsPerDay }}</span>
                </div>
                <div class="rights-item">
                  <span class="rights-label">Par semaine :</span>
                  <span class="rights-value">{{ plan.rights.maxReservationsPerWeek }}</span>
                </div>
                <div class="rights-item">
                  <span class="rights-label">Par mois :</span>
                  <span class="rights-value">{{ plan.rights.maxReservationsPerMonth }}</span>
                </div>
                <div class="rights-item">
                  <span class="rights-label">Heures consécutives max :</span>
                  <span class="rights-value">{{ plan.rights.maxConsecutiveHours }}h</span>
                </div>
                <div class="rights-item">
                  <span class="rights-label">Réservation à l'avance :</span>
                  <span class="rights-value">{{ plan.rights.advanceBookingDays }} jour(s)</span>
                </div>
              </div>
            </div>

            <div class="rights-section">
              <h4>Accès spéciaux</h4>
              <div class="rights-list">
                <div class="rights-item">
                  <span class="rights-label">Salles de réunion :</span>
                  <nz-tag [nzColor]="plan.rights.canBookMeetingRooms ? 'success' : 'default'">
                    {{ plan.rights.canBookMeetingRooms ? 'Autorisé' : 'Non autorisé' }}
                  </nz-tag>
                </div>
                <div class="rights-item">
                  <span class="rights-label">Zones premium :</span>
                  <nz-tag [nzColor]="plan.rights.canAccessPremiumAreas ? 'success' : 'default'">
                    {{ plan.rights.canAccessPremiumAreas ? 'Autorisé' : 'Non autorisé' }}
                  </nz-tag>
                </div>
              </div>
            </div>
          </div>
        </nz-card>

        <!-- Créneaux horaires autorisés -->
        <nz-card nzTitle="Créneaux horaires autorisés" class="info-card">
          <div class="time-slots-list" *ngIf="plan.rights.allowedTimeSlots && plan.rights.allowedTimeSlots.length > 0; else noTimeSlots">
            <div class="time-slot-item" *ngFor="let slot of plan.rights.allowedTimeSlots">
              <nz-icon nzType="clock-circle" nzTheme="outline"></nz-icon>
              <span>{{ slot.start }} - {{ slot.end }}</span>
            </div>
          </div>
          <ng-template #noTimeSlots>
            <nz-empty
              nzNotFoundImage="simple"
              nzNotFoundContent="Aucun créneau horaire défini"
            ></nz-empty>
          </ng-template>
        </nz-card>

        <!-- Jours autorisés -->
        <nz-card nzTitle="Jours d'accès autorisés" class="info-card">
          <div class="allowed-days" *ngIf="plan.rights.allowedDays && plan.rights.allowedDays.length > 0; else noAllowedDays">
            <nz-tag
              *ngFor="let day of plan.rights.allowedDays"
              nzColor="blue"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ getDaysText([day]) }}
            </nz-tag>
          </div>
          <ng-template #noAllowedDays>
            <nz-empty
              nzNotFoundImage="simple"
              nzNotFoundContent="Aucun jour spécifique défini"
            ></nz-empty>
          </ng-template>
        </nz-card>

        <!-- Salles incluses -->
        <nz-card nzTitle="Salles incluses" class="info-card">
          <div class="included-rooms" *ngIf="plan.rights.includedRooms && plan.rights.includedRooms.length > 0; else noIncludedRooms">
            <nz-tag
              *ngFor="let room of plan.rights.includedRooms"
              nzColor="green"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ room }}
            </nz-tag>
          </div>
          <ng-template #noIncludedRooms>
            <nz-empty
              nzNotFoundImage="simple"
              nzNotFoundContent="Aucune salle spécifique incluse"
            ></nz-empty>
          </ng-template>
        </nz-card>

        <!-- Fonctionnalités -->
        <nz-card nzTitle="Fonctionnalités incluses" class="info-card">
          <div class="features-list" *ngIf="plan.features && plan.features.length > 0; else noFeatures">
            <div class="feature-item" *ngFor="let feature of plan.features">
              <nz-icon nzType="check-circle" nzTheme="outline" style="color: #52c41a;"></nz-icon>
              <span>{{ feature }}</span>
            </div>
          </div>
          <ng-template #noFeatures>
            <nz-empty
              nzNotFoundImage="simple"
              nzNotFoundContent="Aucune fonctionnalité spéciale"
            ></nz-empty>
          </ng-template>
        </nz-card>
      </div>
    </div>
  </div>

  <!-- Template de chargement -->
  <ng-template #loadingTemplate>
    <div class="loading-container">
      <nz-spin nzSize="large" nzTip="Chargement des détails du plan...">
        <div class="loading-content"></div>
      </nz-spin>
    </div>
  </ng-template>
</div>
