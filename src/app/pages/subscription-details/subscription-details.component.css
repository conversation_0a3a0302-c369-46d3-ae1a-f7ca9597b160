/* Page container */
.subscription-details-page {
  padding: 0;
  min-height: 100vh;
}

/* Page content */
.page-content {
  max-width: 1200px;
  margin: 0 auto;
}

.subscription-details-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* Header */
.details-header {
  padding: 32px;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.plan-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

.plan-basic-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.plan-name {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0;
}

.plan-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.4;
}

.plan-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-actions button {
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 16px !important;
  font-weight: 500 !important;
}

/* Content */
.details-content {
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Cards */
.info-card {
  border-radius: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #E5E5EA !important;
}

.info-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 20px 24px !important;
}

.info-card .ant-card-head-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.info-card .ant-card-body {
  padding: 24px !important;
}

/* Grille d'informations responsive */
.subscription-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #1C1C1E;
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1.4;
  flex-wrap: wrap;
}

/* Price value */
.price-value {
  font-size: 24px;
  font-weight: 700;
  color: #6E56CF;
}

/* Rights grid */
.rights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.rights-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #6E56CF;
}

.rights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rights-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.rights-label {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.rights-value {
  font-size: 14px;
  color: #1C1C1E;
  font-weight: 600;
}

/* Time slots */
.time-slots-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.time-slot-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #F8F9FA;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1C1C1E;
}

.time-slot-item nz-icon {
  color: #6E56CF;
}

/* Allowed days and rooms */
.allowed-days,
.included-rooms {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Features */
.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #F8F9FA;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1C1C1E;
}

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  width: 100px;
  height: 100px;
}

/* Responsive */
@media (max-width: 768px) {
  .subscription-details-page {
    padding: 16px;
  }

  .details-header {
    padding: 24px;
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .plan-info {
    flex-direction: column;
    text-align: center;
  }

  .plan-name {
    font-size: 24px;
  }

  .plan-icon {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }

  .details-content {
    padding: 20px;
  }

  /* Grille d'informations responsive sur mobile */
  .subscription-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-label {
    font-size: 12px;
  }

  .info-value {
    font-size: 14px;
  }

  .price-value {
    font-size: 20px;
  }

  .rights-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .time-slots-list {
    grid-template-columns: 1fr;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Tags personnalisés */
.ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  border: none !important;
}

/* Empty states */
.ant-empty {
  padding: 40px 20px !important;
}

.ant-empty-description {
  color: #8E8E93 !important;
  font-size: 14px !important;
}

/* Descriptions */
.ant-descriptions-item-label {
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.ant-descriptions-item-content {
  color: #1C1C1E !important;
}
