/* Page container */
.space-details-page {
  padding: 0;
  min-height: 100vh;
}

/* Page content */
.page-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px 0;
  border-bottom: 1px solid #E5E5EA;
}

.header-content {
  flex: 1;
}

.space-header-info {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.space-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  flex-shrink: 0;
}

.space-title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.space-status {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.back-button,
.edit-button {
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 20px !important;
  font-weight: 500 !important;
}

.reserve-button {
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 20px !important;
  font-weight: 500 !important;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%) !important;
  border: none !important;
}

/* Space details container */
.space-details-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

/* Cards */
.info-card,
.equipment-card,
.amenities-card,
.rules-card,
.calendar-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.info-card .ant-card-head,
.equipment-card .ant-card-head,
.amenities-card .ant-card-head,
.rules-card .ant-card-head,
.calendar-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  background: #FAFAFA !important;
}

.info-card .ant-card-head-title,
.equipment-card .ant-card-head-title,
.amenities-card .ant-card-head-title,
.rules-card .ant-card-head-title,
.calendar-card .ant-card-head-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

/* Grille d'informations responsive */
.space-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #1C1C1E;
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1.4;
}

.info-value nz-icon {
  color: #6E56CF;
}

/* Equipment grid */
.equipment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.equipment-item {
  padding: 16px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  background: #FAFAFA;
}

.equipment-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 8px 0;
}

.equipment-info p {
  font-size: 14px;
  color: #8E8E93;
  margin: 0 0 12px 0;
}

.equipment-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.equipment-quantity {
  font-size: 14px;
  color: #6E56CF;
  font-weight: 500;
}

/* Amenities */
.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.amenity-tag {
  font-size: 14px !important;
  border-radius: 6px !important;
  padding: 4px 12px !important;
  background: #F0F0F5 !important;
  color: #6E56CF !important;
  border: 1px solid #E5E5EA !important;
}

/* Rules */
.rules-list {
  margin: 0;
  padding-left: 20px;
}

.rules-list li {
  font-size: 14px;
  color: #1C1C1E;
  line-height: 1.6;
  margin-bottom: 8px;
}

.rules-list li:last-child {
  margin-bottom: 0;
}

/* Calendar section */
.calendar-section {
  margin-bottom: 32px;
}

.calendar-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #FAFAFA;
  border-radius: 8px;
  border: 1px solid #E5E5EA;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

/* Légende du calendrier */
.calendar-legend {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-title {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

.legend-items {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
}

.legend-color.confirmed {
  background-color: #6E56CF;
}

.legend-color.pending {
  background-color: #B8A9E8;
}

.legend-color.cancelled {
  background-color: #C7C7CC;
}

/* Calendar container */
.calendar-container {
  margin-top: 20px;
}

/* FullCalendar customization */
.calendar-container ::ng-deep .fc {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.calendar-container ::ng-deep .fc-header-toolbar {
  margin-bottom: 20px;
  padding: 0 10px;
}

/* Header buttons - Style EXACT de la page reservations */
.calendar-container ::ng-deep .fc-header-toolbar {
  margin-bottom: 20px !important;
  padding: 0 10px !important;
}

.calendar-container ::ng-deep .fc-button-primary {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-button-primary:hover {
  background-color: #5A45B8 !important;
  border-color: #5A45B8 !important;
}

.calendar-container ::ng-deep .fc-button-primary:disabled {
  background-color: #E5E5EA !important;
  border-color: #E5E5EA !important;
}

.calendar-container ::ng-deep .fc-today-button {
  background-color: #F0F0F0 !important;
  border-color: #E5E5EA !important;
  color: #1C1C1E !important;
}

.calendar-container ::ng-deep .fc-today-button:hover {
  background-color: #E5E5EA !important;
}

/* Groupement des boutons - Style comme dans l'image de référence */
.calendar-container ::ng-deep .fc-button-group {
  display: inline-flex !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.calendar-container ::ng-deep .fc-button-group .fc-button {
  border-radius: 0 !important;
  margin: 0 !important;
  border-right: none !important;
}

.calendar-container ::ng-deep .fc-button-group .fc-button:first-child {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

.calendar-container ::ng-deep .fc-button-group .fc-button:last-child {
  border-top-right-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
  border-right: 1px solid #d9d9d9 !important;
}

.calendar-container ::ng-deep .fc-button-group .fc-button.fc-button-active {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
  z-index: 1 !important;
}

.calendar-container ::ng-deep .fc-button-group .fc-button:not(.fc-button-active) {
  background-color: white !important;
  border-color: #d9d9d9 !important;
  color: #666 !important;
}

.calendar-container ::ng-deep .fc-button-group .fc-button:not(.fc-button-active):hover {
  background-color: #f0f2ff !important;
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  z-index: 1 !important;
}

.calendar-container ::ng-deep .fc-toolbar-title {
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.calendar-container ::ng-deep .fc-button-primary {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
}

.calendar-container ::ng-deep .fc-button-primary:hover {
  background: #5A47B8 !important;
  border-color: #5A47B8 !important;
}

.calendar-container ::ng-deep .fc-button-primary:focus {
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.2) !important;
}

.calendar-container ::ng-deep .fc-button-primary:disabled {
  background: #D9D9D9 !important;
  border-color: #D9D9D9 !important;
}

.calendar-container ::ng-deep .fc-today-button {
  background: #F0F0F5 !important;
  border-color: #E5E5EA !important;
  color: #6E56CF !important;
}

.calendar-container ::ng-deep .fc-today-button:hover {
  background: #E5E5EA !important;
  border-color: #D9D9D9 !important;
}

/* Calendar grid */
.calendar-container ::ng-deep .fc-daygrid-day {
  border-color: #E5E5EA !important;
}

.calendar-container ::ng-deep .fc-daygrid-day-top {
  padding: 8px !important;
}

.calendar-container ::ng-deep .fc-day-today {
  background: rgba(110, 86, 207, 0.05) !important;
}

.calendar-container ::ng-deep .fc-daygrid-day-number {
  color: #1C1C1E !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 4px !important;
}

/* Events - Style identique à la page reservations */
.calendar-container ::ng-deep .fc-event {
  border-radius: 8px !important;
  border: none !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  min-height: 20px !important;
  display: flex !important;
  align-items: center !important;
  transition: all 0.2s ease !important;
}

.calendar-container ::ng-deep .fc-event:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  filter: brightness(1.1) !important;
}

/* Supprimer complètement les dots et anciens styles des événements */
.calendar-container ::ng-deep .fc-daygrid-event-dot {
  display: none !important;
}

.calendar-container ::ng-deep .fc-event-dot {
  display: none !important;
}

/* Supprimer les anciens styles de points dans le mode Planning */
.calendar-container ::ng-deep .fc-list-event-dot {
  display: none !important;
}

.calendar-container ::ng-deep .fc-list-event-graphic {
  display: none !important;
}

.calendar-container ::ng-deep .fc-list-event:before {
  display: none !important;
}

/* Forcer le style rectangulaire pour tous les modes */
.calendar-container ::ng-deep .fc-event,
.calendar-container ::ng-deep .fc-daygrid-event,
.calendar-container ::ng-deep .fc-timegrid-event,
.calendar-container ::ng-deep .fc-list-event {
  border-radius: 8px !important;
  border: none !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  margin: 1px 2px !important;
  min-height: 20px !important;
  display: flex !important;
  align-items: center !important;
  background-color: inherit !important;
}

/* Supprimer tous les éléments graphiques par défaut */
.calendar-container ::ng-deep .fc-list-event .fc-list-event-graphic,
.calendar-container ::ng-deep .fc-list-event .fc-list-event-dot {
  display: none !important;
}

/* Styles spécifiques pour le mode Planning (liste) */
.calendar-container ::ng-deep .fc-list-event {
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  border-bottom: 1px solid #f0f0f0 !important;
  background: white !important;
  padding: 12px 16px !important;
}

.calendar-container ::ng-deep .fc-list-event-title {
  padding: 8px 12px !important;
  border-radius: 6px !important;
  margin: 0 !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(110, 86, 207, 0.2) !important;
}

/* SOLUTION CORRIGÉE - Couleurs selon le statut par défaut */
.calendar-container ::ng-deep .fc-daygrid-event {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event .fc-event-main {
  background: inherit !important;
  color: inherit !important;
}

/* Forcer selon les classes de statut */
.calendar-container ::ng-deep .fc-daygrid-event[class*="confirmed"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[class*="pending"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[class*="cancelled"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Forcer les couleurs selon le style inline pour le mode Mois */
.calendar-container ::ng-deep .fc-daygrid-event[style*="#6E56CF"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="#6e56cf"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(110, 86, 207)"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#6E56CF"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="#6e56cf"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(110, 86, 207)"] .fc-event-title {
  background: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#B8A9E8"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="#b8a9e8"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(184, 169, 232)"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#B8A9E8"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="#b8a9e8"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(184, 169, 232)"] .fc-event-title {
  background: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#C7C7CC"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="#c7c7cc"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(199, 199, 204)"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#C7C7CC"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="#c7c7cc"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(199, 199, 204)"] .fc-event-title {
  background: #C7C7CC !important;
  color: #333 !important;
}

/* STYLES OPTIMISÉS POUR LES MODES JOUR ET SEMAINE */

/* Mode Jour et Semaine - Événements timeGrid */
.calendar-container ::ng-deep .fc-timegrid-event {
  border-radius: 6px !important;
  border: none !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  margin: 1px !important;
  min-height: 20px !important;
  opacity: 1 !important;
}

/* Couleurs pour le mode Jour et Semaine selon le statut */
.calendar-container ::ng-deep .fc-timegrid-event[style*="#6E56CF"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="#6e56cf"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="rgb(110, 86, 207)"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-timegrid-event[style*="#B8A9E8"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="#b8a9e8"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="rgb(184, 169, 232)"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-timegrid-event[style*="#C7C7CC"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="#c7c7cc"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="rgb(199, 199, 204)"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Couleurs par classe pour le mode Jour et Semaine */
.calendar-container ::ng-deep .fc-timegrid-event[class*="confirmed"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-timegrid-event[class*="pending"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-timegrid-event[class*="cancelled"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* Titres des événements dans les modes Jour et Semaine */
.calendar-container ::ng-deep .fc-timegrid-event .fc-event-title,
.calendar-container ::ng-deep .fc-timegrid-event .fc-event-main {
  background: inherit !important;
  color: inherit !important;
  font-weight: 500 !important;
}

/* Style pour les événements dans la vue mensuelle */
.calendar-container ::ng-deep .fc-daygrid-event {
  border-radius: 6px !important;
  margin: 1px 0 !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Style pour les événements dans la vue semaine/jour */
.calendar-container ::ng-deep .fc-timegrid-event {
  border-radius: 6px !important;
  border: none !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Améliorer le texte des événements */
.calendar-container ::ng-deep .fc-event-title {
  font-weight: 500 !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.calendar-container ::ng-deep .fc-event-time {
  font-weight: 400 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 10px !important;
}

/* Couleurs des événements - identiques à la page reservations */
/* Confirmé - Violet principal */
.calendar-container ::ng-deep .fc-event.confirmed-event,
.calendar-container ::ng-deep .fc-event[style*="#6E56CF"],
.calendar-container ::ng-deep .fc-event[style*="#6e56cf"],
.calendar-container ::ng-deep .fc-event[style*="rgb(110, 86, 207)"] {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
  opacity: 1 !important;
}

.calendar-container ::ng-deep .fc-event.confirmed-event .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="#6E56CF"] .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="#6e56cf"] .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="rgb(110, 86, 207)"] .fc-event-title {
  color: white !important;
}

/* En attente - Violet soft */
.calendar-container ::ng-deep .fc-event.pending-event,
.calendar-container ::ng-deep .fc-event[style*="#B8A9E8"],
.calendar-container ::ng-deep .fc-event[style*="#b8a9e8"],
.calendar-container ::ng-deep .fc-event[style*="rgb(184, 169, 232)"] {
  background-color: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: #333 !important;
  opacity: 1 !important;
}

.calendar-container ::ng-deep .fc-event.pending-event .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="#B8A9E8"] .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="#b8a9e8"] .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="rgb(184, 169, 232)"] .fc-event-title {
  color: #333 !important;
}

/* Annulé - Gris */
.calendar-container ::ng-deep .fc-event.cancelled-event,
.calendar-container ::ng-deep .fc-event[style*="#C7C7CC"],
.calendar-container ::ng-deep .fc-event[style*="#c7c7cc"],
.calendar-container ::ng-deep .fc-event[style*="rgb(199, 199, 204)"] {
  background-color: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
  opacity: 1 !important;
}

.calendar-container ::ng-deep .fc-event.cancelled-event .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="#C7C7CC"] .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="#c7c7cc"] .fc-event-title,
.calendar-container ::ng-deep .fc-event[style*="rgb(199, 199, 204)"] .fc-event-title {
  color: #333 !important;
}

/* Style par défaut */
.calendar-container ::ng-deep .fc-event.default-event {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: white !important;
  opacity: 1 !important;
}

.calendar-container ::ng-deep .fc-event.default-event .fc-event-title {
  color: white !important;
}

/* Styles supprimés - remplacés par ceux de /reservations qui fonctionnent */

/* SOLUTION ULTIME - FORCER LE TEXTE BLANC SUR VIOLET SOFT */
.calendar-container ::ng-deep .fc-event[style*="#B8A9E8"] {
  color: white !important;
  background-color: #B8A9E8 !important;
}

.calendar-container ::ng-deep .fc-event[style*="#B8A9E8"] .fc-event-title {
  color: white !important;
  background-color: transparent !important;
}

.calendar-container ::ng-deep .fc-event[style*="#B8A9E8"] .fc-event-main {
  color: white !important;
  background-color: transparent !important;
}

.calendar-container ::ng-deep .fc-event[style*="#B8A9E8"] * {
  color: white !important;
}

/* FORCER AUSSI PAR CLASSE */
.calendar-container ::ng-deep .fc-event.pending-event {
  color: white !important;
  background-color: #B8A9E8 !important;
}

.calendar-container ::ng-deep .fc-event.pending-event .fc-event-title {
  color: white !important;
}

.calendar-container ::ng-deep .fc-event.pending-event .fc-event-main {
  color: white !important;
}

.calendar-container ::ng-deep .fc-event.pending-event * {
  color: white !important;
}

/* FORCER GLOBALEMENT TOUS LES ÉVÉNEMENTS VIOLETS SOFT */
.calendar-container ::ng-deep .fc-daygrid-event[style*="#B8A9E8"],
.calendar-container ::ng-deep .fc-timegrid-event[style*="#B8A9E8"] {
  color: white !important;
  background-color: #B8A9E8 !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#B8A9E8"] *,
.calendar-container ::ng-deep .fc-timegrid-event[style*="#B8A9E8"] * {
  color: white !important;
}

/* SOLUTION NUCLÉAIRE - FORCER BLANC SUR TOUT CE QUI EST VIOLET SOFT */
.calendar-container ::ng-deep [style*="#B8A9E8"] {
  color: white !important;
}

.calendar-container ::ng-deep [style*="#B8A9E8"] * {
  color: white !important;
}

.calendar-container ::ng-deep [class*="pending"] {
  color: white !important;
}

.calendar-container ::ng-deep [class*="pending"] * {
  color: white !important;
}

/* Styles finaux copiés de /reservations - Hover et interactions */
.calendar-container ::ng-deep .fc-event:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* SOLUTION CORRIGÉE - Couleurs selon le statut par défaut */
.calendar-container ::ng-deep .fc-daygrid-event {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event .fc-event-main {
  background: inherit !important;
  color: inherit !important;
}

/* Forcer selon les classes de statut */
.calendar-container ::ng-deep .fc-daygrid-event[class*="confirmed"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[class*="pending"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: #333 !important;
}

/* Forcer les couleurs selon le style inline pour le mode Mois */
.calendar-container ::ng-deep .fc-daygrid-event[style*="#6E56CF"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="#6e56cf"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(110, 86, 207)"] {
  background: #6E56CF !important;
  border-color: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#6E56CF"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="#6e56cf"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(110, 86, 207)"] .fc-event-title {
  background: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#B8A9E8"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="#b8a9e8"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(184, 169, 232)"] {
  background: #B8A9E8 !important;
  border-color: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#B8A9E8"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="#b8a9e8"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(184, 169, 232)"] .fc-event-title {
  background: #B8A9E8 !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#C7C7CC"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="#c7c7cc"],
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(199, 199, 204)"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[style*="#C7C7CC"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="#c7c7cc"] .fc-event-title,
.calendar-container ::ng-deep .fc-daygrid-event[style*="rgb(199, 199, 204)"] .fc-event-title {
  background: #C7C7CC !important;
  color: #333 !important;
}

.calendar-container ::ng-deep .fc-daygrid-event[class*="cancelled"] {
  background: #C7C7CC !important;
  border-color: #C7C7CC !important;
  color: #333 !important;
}

/* FORCE ABSOLUE POUR LE MODE PLANNING - Tous les sélecteurs possibles */
.calendar-container ::ng-deep .fc-listMonth-view .fc-list-event-title,
.calendar-container ::ng-deep .fc-listWeek-view .fc-list-event-title,
.calendar-container ::ng-deep .fc-listDay-view .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-view .fc-list-event-title {
  background: #6E56CF !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  margin: 0 !important;
  font-weight: 500 !important;
}

/* Forcer sur tous les éléments de titre dans le mode Planning */
.calendar-container ::ng-deep .fc-list-table .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-table .fc-event-title,
.calendar-container ::ng-deep .fc-list-table .fc-event-main {
  background: #6E56CF !important;
  color: white !important;
}

/* Forcer sur les événements par style inline dans le mode Planning */
.calendar-container ::ng-deep .fc-list-event[style*="#6E56CF"] .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-event[style*="#6e56cf"] .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-event[style*="rgb(110, 86, 207)"] .fc-list-event-title {
  background: #6E56CF !important;
  color: white !important;
}

.calendar-container ::ng-deep .fc-list-event[style*="#B8A9E8"] .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-event[style*="#b8a9e8"] .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-event[style*="rgb(184, 169, 232)"] .fc-list-event-title {
  background: #B8A9E8 !important;
  color: #333 !important;
}

.calendar-container ::ng-deep .fc-list-event[style*="#C7C7CC"] .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-event[style*="#c7c7cc"] .fc-list-event-title,
.calendar-container ::ng-deep .fc-list-event[style*="rgb(199, 199, 204)"] .fc-list-event-title {
  background: #C7C7CC !important;
  color: #333 !important;
}

/* Time grid */
.calendar-container ::ng-deep .fc-timegrid-slot {
  border-color: #F0F0F0 !important;
  height: 30px !important;
}

.calendar-container ::ng-deep .fc-timegrid-slot-label {
  font-size: 12px !important;
  color: #8E8E93 !important;
}

.calendar-container ::ng-deep .fc-timegrid-axis {
  border-color: #E5E5EA !important;
}

.calendar-container ::ng-deep .fc-timegrid-divider {
  border-color: #E5E5EA !important;
}

/* List view */
.calendar-container ::ng-deep .fc-list-event {
  border-left: 4px solid !important;
  padding: 8px 12px !important;
}

.calendar-container ::ng-deep .fc-list-event-title {
  font-weight: 500 !important;
  color: #1C1C1E !important;
}

.calendar-container ::ng-deep .fc-list-event-time {
  color: #6E56CF !important;
  font-weight: 500 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .space-details-page {
    padding: 0 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: left;
    padding: 20px 0;
  }

  .space-header-info {
    flex-direction: row;
    text-align: left;
    gap: 16px;
    align-items: flex-start;
  }

  .space-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
    flex-direction: column;
    gap: 12px;
  }

  .header-actions button {
    width: 100% !important;
    justify-content: center !important;
  }

  .page-title {
    font-size: 20px;
    line-height: 1.3;
  }

  .page-description {
    font-size: 14px;
  }

  .space-status {
    margin-top: 8px;
  }

  .space-status .ant-tag {
    font-size: 11px;
    padding: 2px 6px;
  }

  /* Cards responsive */
  .info-card .ant-card-body,
  .equipment-card .ant-card-body,
  .amenities-card .ant-card-body,
  .rules-card .ant-card-body,
  .calendar-card .ant-card-body {
    padding: 16px !important;
  }

  /* Grille d'informations responsive */
  .space-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-item.full-width {
    grid-column: 1;
  }

  .info-label {
    font-size: 12px;
  }

  .info-value {
    font-size: 14px;
  }

  .equipment-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .equipment-item {
    padding: 12px;
  }

  .equipment-name {
    font-size: 13px;
  }

  .equipment-description {
    font-size: 11px;
  }

  /* Amenities responsive */
  .amenities-list {
    gap: 8px;
  }

  .amenity-tag {
    font-size: 11px !important;
    padding: 2px 6px !important;
  }

  /* Rules responsive */
  .rules-list {
    font-size: 13px;
    padding-left: 16px;
  }

  .rules-list li {
    margin-bottom: 8px;
    line-height: 1.4;
  }

  /* Calendar responsive */
  .calendar-container ::ng-deep .fc-header-toolbar {
    flex-direction: column;
    gap: 10px;
    padding: 8px;
  }

  .calendar-container ::ng-deep .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }

  .calendar-container ::ng-deep .fc-button {
    font-size: 12px !important;
    padding: 4px 8px !important;
  }

  .calendar-container ::ng-deep .fc-button-group {
    font-size: 12px;
  }

  .calendar-container ::ng-deep .fc-toolbar-title {
    font-size: 16px !important;
  }

  .calendar-container ::ng-deep .fc-daygrid-event {
    font-size: 11px !important;
  }

  .calendar-container ::ng-deep .fc-col-header-cell {
    font-size: 12px !important;
  }

  .calendar-container ::ng-deep .fc-daygrid-day-number {
    font-size: 12px !important;
  }

  /* SOLUTION FINALE - Scroll horizontal UNIQUEMENT sur la grille du calendrier */
  .calendar-container ::ng-deep .fc-view-harness {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #E5E5EA transparent;
  }

  .calendar-container ::ng-deep .fc-view-harness::-webkit-scrollbar {
    height: 6px;
  }

  .calendar-container ::ng-deep .fc-view-harness::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .calendar-container ::ng-deep .fc-view-harness::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  /* Élargir les cases du calendrier pour forcer le scroll */
  .calendar-container ::ng-deep .fc-daygrid-day {
    min-width: 80px !important;
    width: 80px !important;
  }

  .calendar-container ::ng-deep .fc-col-header-cell {
    min-width: 80px !important;
    width: 80px !important;
  }

  /* Forcer une largeur minimale pour la table du calendrier */
  .calendar-container ::ng-deep .fc-daygrid-body table,
  .calendar-container ::ng-deep .fc-col-header table,
  .calendar-container ::ng-deep .fc-scrollgrid-sync-table {
    min-width: 600px !important; /* Force le scroll horizontal */
    width: 600px !important;
  }

  .calendar-container ::ng-deep .fc-daygrid {
    min-width: 600px !important;
  }

  /* Pour les vues semaine et jour */
  .calendar-container ::ng-deep .fc-timegrid-col {
    min-width: 100px !important;
  }

  .calendar-container ::ng-deep .fc-timegrid-axis {
    min-width: 60px !important;
  }
}

/* Responsive pour très petits écrans */
@media (max-width: 480px) {
  /* Élargir encore plus les cases pour forcer le scroll */
  .calendar-container ::ng-deep .fc-daygrid-day {
    min-width: 70px !important;
    width: 70px !important;
  }

  .calendar-container ::ng-deep .fc-col-header-cell {
    min-width: 70px !important;
    width: 70px !important;
  }

  /* Forcer une largeur minimale pour la table du calendrier */
  .calendar-container ::ng-deep .fc-daygrid-body table,
  .calendar-container ::ng-deep .fc-col-header table {
    min-width: 490px !important; /* 7 jours × 70px */
  }

  .calendar-container ::ng-deep .fc-daygrid-day-number {
    font-size: 14px !important;
    padding: 6px !important;
  }

  .calendar-container ::ng-deep .fc-col-header-cell-cushion {
    font-size: 12px !important;
    padding: 8px 4px !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .space-details-page {
    background: #1C1C1E;
    color: #FFFFFF;
  }

  .page-title {
    color: #FFFFFF;
  }

  .page-description {
    color: #8E8E93;
  }

  .equipment-item {
    background: #2C2C2E;
    border-color: #3A3A3C;
  }

  .equipment-info h4 {
    color: #FFFFFF;
  }

  .rules-list li {
    color: #FFFFFF;
  }

  .calendar-filters {
    background: #2C2C2E;
    border-color: #3A3A3C;
  }

  .filter-item label {
    color: #FFFFFF;
  }
}
