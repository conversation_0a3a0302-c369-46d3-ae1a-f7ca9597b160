import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router } from '@angular/router';

// Ng-Zorro imports
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';

import { SubscriptionService } from '../../services/subscription.service';
import {
  SubscriptionPlan,
  SubscriptionType,
  MembershipType,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest
} from '../../models/subscription.model';

@Component({
  selector: 'app-subscriptions',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzModalModule,
    NzFormModule,
    NzTagModule,
    NzCardModule,
    NzSpinModule,
    NzToolTipModule,
    NzDropDownModule,
    NzModalModule,
    NzMessageModule,
    NzInputNumberModule,
    NzCheckboxModule,
    NzTimePickerModule
  ],
  templateUrl: './subscriptions.component.html',
  styleUrl: './subscriptions.component.css'
})
export class SubscriptionsComponent implements OnInit {

  // Signaux pour la réactivité
  private plansSignal = signal<SubscriptionPlan[]>([]);
  private loadingSignal = signal<boolean>(false);
  private searchTermSignal = signal<string>('');
  private selectedTypeSignal = signal<SubscriptionType | 'all'>('all');
  private selectedMembershipSignal = signal<MembershipType | 'all'>('all');

  // Getters pour les templates
  get plans() { return this.plansSignal(); }
  get loading() { return this.loadingSignal(); }

  // Configuration des colonnes pour le tri
  listOfColumn = [
    {
      title: 'Nom',
      compare: (a: SubscriptionPlan, b: SubscriptionPlan) => a.name.localeCompare(b.name),
      priority: false
    },
    {
      title: 'Type',
      compare: (a: SubscriptionPlan, b: SubscriptionPlan) => a.type.localeCompare(b.type),
      priority: false
    },
    {
      title: 'Catégorie',
      compare: (a: SubscriptionPlan, b: SubscriptionPlan) => a.membershipTypes.join(',').localeCompare(b.membershipTypes.join(',')),
      priority: false
    },
    {
      title: 'Prix',
      compare: (a: SubscriptionPlan, b: SubscriptionPlan) => a.price - b.price,
      priority: false
    },
    {
      title: 'Durée',
      compare: (a: SubscriptionPlan, b: SubscriptionPlan) => a.duration - b.duration,
      priority: false
    }
  ];

  // Signaux calculés
  filteredPlans = computed(() => {
    const plans = this.plansSignal();
    const searchTerm = this.searchTermSignal().toLowerCase();
    const selectedType = this.selectedTypeSignal();
    const selectedMembership = this.selectedMembershipSignal();

    return plans.filter(plan => {
      const matchesSearch = !searchTerm ||
        plan.name.toLowerCase().includes(searchTerm) ||
        plan.description.toLowerCase().includes(searchTerm);

      const matchesType = selectedType === 'all' || plan.type === selectedType;
      const matchesMembership = selectedMembership === 'all' || plan.membershipTypes.includes(selectedMembership);

      return matchesSearch && matchesType && matchesMembership;
    });
  });

  // Propriétés pour les modales
  isCreateModalVisible = false;
  isEditModalVisible = false;
  selectedPlan: SubscriptionPlan | null = null;

  // Formulaires
  createForm!: FormGroup;
  editForm!: FormGroup;

  // Options pour les selects
  typeOptions = [
    { label: 'Tous les types', value: 'all' },
    { label: 'Journalier', value: SubscriptionType.DAILY },
    { label: 'Hebdomadaire', value: SubscriptionType.WEEKLY },
    { label: 'Mensuel', value: SubscriptionType.MONTHLY },
    { label: 'Flexible', value: SubscriptionType.FLEXIBLE }
  ];

  membershipOptions = [
    { label: 'Toutes les catégories', value: 'all' },
    { label: 'Étudiant', value: MembershipType.STUDENT },
    { label: 'Professionnel', value: MembershipType.PROFESSIONAL },
    { label: 'Premium', value: MembershipType.PREMIUM }
  ];

  subscriptionTypeOptions = [
    { label: 'Journalier', value: SubscriptionType.DAILY },
    { label: 'Hebdomadaire', value: SubscriptionType.WEEKLY },
    { label: 'Mensuel', value: SubscriptionType.MONTHLY },
    { label: 'Flexible', value: SubscriptionType.FLEXIBLE }
  ];

  membershipTypeOptions = [
    { label: 'Étudiant', value: MembershipType.STUDENT },
    { label: 'Professionnel', value: MembershipType.PROFESSIONAL },
    { label: 'Premium', value: MembershipType.PREMIUM }
  ];

  dayOptions = [
    { label: 'Lundi', value: 'monday' },
    { label: 'Mardi', value: 'tuesday' },
    { label: 'Mercredi', value: 'wednesday' },
    { label: 'Jeudi', value: 'thursday' },
    { label: 'Vendredi', value: 'friday' },
    { label: 'Samedi', value: 'saturday' },
    { label: 'Dimanche', value: 'sunday' }
  ];

  // Enums pour les templates
  SubscriptionType = SubscriptionType;
  MembershipType = MembershipType;

  constructor(
    private subscriptionService: SubscriptionService,
    private fb: FormBuilder,
    private message: NzMessageService,
    private router: Router,
    private modal: NzModalService
  ) {
    this.initializeForms();
  }

  ngOnInit() {
    this.loadPlans();
  }

  private initializeForms() {
    this.createForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required]],
      type: [null, [Validators.required]],
      membershipType: [null, [Validators.required]],
      price: [0, [Validators.required, Validators.min(0)]],
      duration: [1, [Validators.required, Validators.min(1)]],
      features: this.fb.array([]),
      rights: this.fb.group({
        maxReservationsPerDay: [1, [Validators.required, Validators.min(1)]],
        maxReservationsPerWeek: [5, [Validators.required, Validators.min(1)]],
        maxReservationsPerMonth: [20, [Validators.required, Validators.min(1)]],
        allowedTimeSlots: this.fb.array([]),
        allowedDays: [[], [Validators.required]],
        includedRooms: [[], [Validators.required]],
        canBookMeetingRooms: [false],
        canAccessPremiumAreas: [false],
        maxConsecutiveHours: [4, [Validators.required, Validators.min(1)]],
        advanceBookingDays: [3, [Validators.required, Validators.min(1)]]
      })
    });

    this.editForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required]],
      type: [null, [Validators.required]],
      membershipType: [null, [Validators.required]],
      price: [0, [Validators.required, Validators.min(0)]],
      duration: [1, [Validators.required, Validators.min(1)]],
      features: this.fb.array([]),
      isActive: [true],
      rights: this.fb.group({
        maxReservationsPerDay: [1, [Validators.required, Validators.min(1)]],
        maxReservationsPerWeek: [5, [Validators.required, Validators.min(1)]],
        maxReservationsPerMonth: [20, [Validators.required, Validators.min(1)]],
        allowedTimeSlots: this.fb.array([]),
        allowedDays: [[], [Validators.required]],
        includedRooms: [[], [Validators.required]],
        canBookMeetingRooms: [false],
        canAccessPremiumAreas: [false],
        maxConsecutiveHours: [4, [Validators.required, Validators.min(1)]],
        advanceBookingDays: [3, [Validators.required, Validators.min(1)]]
      })
    });

    // Ajouter un créneau horaire par défaut
    this.addTimeSlot(this.createForm);
    this.addTimeSlot(this.editForm);

    // Ajouter une fonctionnalité par défaut
    this.addFeature(this.createForm);
    this.addFeature(this.editForm);
  }

  // Gestion des créneaux horaires
  get createTimeSlots() {
    return this.createForm.get('rights.allowedTimeSlots') as FormArray;
  }

  get editTimeSlots() {
    return this.editForm.get('rights.allowedTimeSlots') as FormArray;
  }

  addTimeSlot(form: FormGroup) {
    const timeSlots = form.get('rights.allowedTimeSlots') as FormArray;
    const timeSlotGroup = this.fb.group({
      start: ['08:00', Validators.required],
      end: ['18:00', Validators.required]
    });
    timeSlots.push(timeSlotGroup);
  }

  removeTimeSlot(form: FormGroup, index: number) {
    const timeSlots = form.get('rights.allowedTimeSlots') as FormArray;
    if (timeSlots.length > 1) {
      timeSlots.removeAt(index);
    }
  }

  // Gestion des fonctionnalités
  get createFeatures() {
    return this.createForm.get('features') as FormArray;
  }

  get editFeatures() {
    return this.editForm.get('features') as FormArray;
  }

  addFeature(form: FormGroup) {
    const features = form.get('features') as FormArray;
    features.push(this.fb.control('', Validators.required));
  }

  removeFeature(form: FormGroup, index: number) {
    const features = form.get('features') as FormArray;
    if (features.length > 1) {
      features.removeAt(index);
    }
  }

  // Chargement des données
  loadPlans() {
    this.loadingSignal.set(true);
    this.subscriptionService.getPlans().subscribe({
      next: (plans) => {
        this.plansSignal.set(plans);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des plans:', error);
        this.message.error('Erreur lors du chargement des plans');
        this.loadingSignal.set(false);
      }
    });
  }

  // Filtres
  onSearchChange(term: string) {
    this.searchTermSignal.set(term);
  }

  onTypeFilterChange(type: SubscriptionType | 'all') {
    this.selectedTypeSignal.set(type);
  }

  onMembershipFilterChange(membership: MembershipType | 'all') {
    this.selectedMembershipSignal.set(membership);
  }

  // Effacer tous les filtres
  clearFilters() {
    this.searchTermSignal.set('');
    this.selectedTypeSignal.set('all');
    this.selectedMembershipSignal.set('all');

    // Réinitialiser le champ de recherche dans le DOM
    const searchInput = document.querySelector('.filters-card input[nz-input]') as HTMLInputElement;
    if (searchInput) {
      searchInput.value = '';
    }
  }

  // Navigation vers les formulaires
  navigateToCreate() {
    this.router.navigate(['/subscriptions/new']);
  }

  navigateToEdit(plan: SubscriptionPlan) {
    this.router.navigate(['/subscriptions/edit', plan.id]);
  }

  showPlanDetails(plan: SubscriptionPlan) {
    this.router.navigate(['/subscriptions/details', plan.id]);
  }

  private populateEditForm(plan: SubscriptionPlan) {
    // Réinitialiser les FormArrays
    const editTimeSlots = this.editForm.get('rights.allowedTimeSlots') as FormArray;
    const editFeatures = this.editForm.get('features') as FormArray;

    editTimeSlots.clear();
    editFeatures.clear();

    // Remplir les créneaux horaires
    plan.rights.allowedTimeSlots.forEach(slot => {
      const timeSlotGroup = this.fb.group({
        start: [slot.start, Validators.required],
        end: [slot.end, Validators.required]
      });
      editTimeSlots.push(timeSlotGroup);
    });

    // Remplir les fonctionnalités
    plan.features.forEach(feature => {
      editFeatures.push(this.fb.control(feature, Validators.required));
    });

    // Remplir le reste du formulaire
    this.editForm.patchValue({
      name: plan.name,
      description: plan.description,
      type: plan.type,
      membershipTypes: plan.membershipTypes,
      price: plan.price,
      duration: plan.duration,
      isActive: plan.isActive,
      rights: {
        maxReservationsPerDay: plan.rights.maxReservationsPerDay,
        maxReservationsPerWeek: plan.rights.maxReservationsPerWeek,
        maxReservationsPerMonth: plan.rights.maxReservationsPerMonth,
        allowedDays: plan.rights.allowedDays,
        includedRooms: plan.rights.includedRooms,
        canBookMeetingRooms: plan.rights.canBookMeetingRooms,
        canAccessPremiumAreas: plan.rights.canAccessPremiumAreas,
        maxConsecutiveHours: plan.rights.maxConsecutiveHours,
        advanceBookingDays: plan.rights.advanceBookingDays
      }
    });
  }

  // CRUD Operations
  createPlan() {
    if (this.createForm.valid) {
      const formValue = this.createForm.value;
      const request: CreateSubscriptionPlanRequest = {
        name: formValue.name,
        description: formValue.description,
        type: formValue.type,
        membershipTypes: formValue.membershipTypes,
        price: formValue.price,
        duration: formValue.duration,
        features: formValue.features.filter((f: string) => f.trim()),
        rights: {
          ...formValue.rights,
          allowedTimeSlots: formValue.rights.allowedTimeSlots
        }
      };

      this.subscriptionService.createPlan(request).subscribe({
        next: (plan) => {
          this.message.success('Plan créé avec succès');
          this.loadPlans();
        },
        error: (error) => {
          console.error('Erreur lors de la création:', error);
          this.message.error('Erreur lors de la création du plan');
        }
      });
    } else {
      this.message.warning('Veuillez remplir tous les champs requis');
    }
  }

  updatePlan() {
    if (this.editForm.valid && this.selectedPlan) {
      const formValue = this.editForm.value;
      const request: UpdateSubscriptionPlanRequest = {
        name: formValue.name,
        description: formValue.description,
        type: formValue.type,
        membershipTypes: formValue.membershipTypes,
        price: formValue.price,
        duration: formValue.duration,
        features: formValue.features.filter((f: string) => f.trim()),
        isActive: formValue.isActive,
        rights: {
          ...formValue.rights,
          allowedTimeSlots: formValue.rights.allowedTimeSlots
        }
      };

      this.subscriptionService.updatePlan(this.selectedPlan.id, request).subscribe({
        next: (plan) => {
          this.message.success('Plan modifié avec succès');
          this.loadPlans();
        },
        error: (error) => {
          console.error('Erreur lors de la modification:', error);
          this.message.error('Erreur lors de la modification du plan');
        }
      });
    } else {
      this.message.warning('Veuillez remplir tous les champs requis');
    }
  }

  confirmDeletePlan(plan: SubscriptionPlan) {
    this.modal.confirm({
      nzTitle: 'Supprimer le plan',
      nzContent: `Êtes-vous sûr de vouloir supprimer le plan <strong>${plan.name}</strong> ?<br><br>Cette action est irréversible et affectera tous les membres ayant ce plan.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        this.deletePlan(plan);
      }
    });
  }

  private deletePlan(plan: SubscriptionPlan) {
    this.subscriptionService.deletePlan(plan.id).subscribe({
      next: () => {
        this.message.success('Plan supprimé avec succès');
        this.loadPlans();
      },
      error: (error) => {
        console.error('Erreur lors de la suppression:', error);
        this.message.error('Erreur lors de la suppression du plan');
      }
    });
  }

  // Méthodes utilitaires
  getTypeLabel(type: SubscriptionType): string {
    const option = this.subscriptionTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
  }

  getMembershipLabel(membership: MembershipType): string {
    const option = this.membershipTypeOptions.find(opt => opt.value === membership);
    return option ? option.label : membership;
  }

  getTypeColor(type: SubscriptionType): string {
    switch (type) {
      case SubscriptionType.DAILY: return 'blue';
      case SubscriptionType.WEEKLY: return 'green';
      case SubscriptionType.MONTHLY: return 'orange';
      case SubscriptionType.FLEXIBLE: return 'purple';
      default: return 'default';
    }
  }

  getMembershipColor(membership: MembershipType): string {
    switch (membership) {
      case MembershipType.STUDENT: return 'cyan';
      case MembershipType.PROFESSIONAL: return 'geekblue';
      case MembershipType.PREMIUM: return 'gold';
      default: return 'default';
    }
  }

  getFilteredPlans() {
    return this.filteredPlans();
  }

  // Méthodes pour les salles (mock)
  getRoomOptions() {
    return [
      { label: 'Salle 1', value: 'room1' },
      { label: 'Salle 2', value: 'room2' },
      { label: 'Salle 3', value: 'room3' },
      { label: 'Salle 4', value: 'room4' },
      { label: 'Salle 5', value: 'room5' },
      { label: 'Salle de réunion 1', value: 'meeting1' },
      { label: 'Salle de réunion 2', value: 'meeting2' }
    ];
  }
}
