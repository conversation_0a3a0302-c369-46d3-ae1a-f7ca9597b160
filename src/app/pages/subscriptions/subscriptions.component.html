<div class="subscriptions-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
        Gestion des Abonnements
      </h1>
      <p class="page-description"><PERSON><PERSON><PERSON> les plans d'abonnement et formules</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="primary" nzSize="large" (click)="navigateToCreate()" class="add-button">
        <nz-icon nzType="plus"></nz-icon>
        Nouveau Plan
      </button>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <nz-card class="filters-card" nzTitle="Filtres">
    <div class="filters-row">
      <div class="filter-item">
        <label>Rechercher</label>
        <nz-input-group nzPrefixIcon="search">
          <input
            nz-input
            placeholder="Nom, description..."
            (input)="onSearchChange($any($event.target).value)"
          />
        </nz-input-group>
      </div>

      <div class="filter-item">
        <label>Type</label>
        <nz-select
          nzPlaceHolder="Tous les types"
          (ngModelChange)="onTypeFilterChange($event)"
          [ngModel]="'all'"
        >
          <nz-option
            *ngFor="let option of typeOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Catégorie</label>
        <nz-select
          nzPlaceHolder="Toutes les catégories"
          (ngModelChange)="onMembershipFilterChange($event)"
          [ngModel]="'all'"
        >
          <nz-option
            *ngFor="let option of membershipOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>&nbsp;</label>
        <button
          nz-button
          nzType="default"
          (click)="clearFilters()"
          class="clear-filters-btn"
        >
          <nz-icon nzType="clear"></nz-icon>
          Effacer les filtres
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Tableau des plans -->
  <nz-card class="table-card" nzTitle="Plans d'Abonnement">
    <nz-spin [nzSpinning]="loading">
      <div class="table-wrapper">
        <nz-table
          #sortTable
          [nzData]="getFilteredPlans()"
          [nzPageSize]="10"
          [nzShowSizeChanger]="true"
          [nzPageSizeOptions]="[10, 20, 50]"
          nzShowQuickJumper
          [nzScroll]="{ x: '800px' }"
        >
        <thead>
          <tr>
            <th [nzSortFn]="listOfColumn[0].compare" [nzSortPriority]="listOfColumn[0].priority"
                nzLeft="0px" nzWidth="200px">
              {{ listOfColumn[0].title }}
            </th>
            <th [nzSortFn]="listOfColumn[1].compare" [nzSortPriority]="listOfColumn[1].priority" nzWidth="120px">
              {{ listOfColumn[1].title }}
            </th>
            <th [nzSortFn]="listOfColumn[2].compare" [nzSortPriority]="listOfColumn[2].priority" nzWidth="100px">
              {{ listOfColumn[2].title }}
            </th>
            <th [nzSortFn]="listOfColumn[3].compare" [nzSortPriority]="listOfColumn[3].priority" nzWidth="120px">
              {{ listOfColumn[3].title }}
            </th>
            <th [nzSortFn]="listOfColumn[4].compare" [nzSortPriority]="listOfColumn[4].priority" nzWidth="100px">
              {{ listOfColumn[4].title }}
            </th>
            <th nzWidth="100px">Statut</th>
            <th nzWidth="200px">Fonctionnalités</th>
            <th nzWidth="80px">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let plan of sortTable.data">
            <td nzLeft="0px">
              <div class="plan-info">
                <div class="plan-name">
                  <a (click)="showPlanDetails(plan)" style="color: inherit; text-decoration: none; cursor: pointer;">
                    {{ plan.name }}
                  </a>
                </div>
                <div class="plan-description">{{ plan.description }}</div>
              </div>
            </td>
            <td>
              <nz-tag [nzColor]="getTypeColor(plan.type)">
                {{ getTypeLabel(plan.type) }}
              </nz-tag>
            </td>
            <td>
              <nz-tag
                *ngFor="let membershipType of plan.membershipTypes"
                [nzColor]="getMembershipColor(membershipType)"
                style="margin-right: 4px;"
              >
                {{ getMembershipLabel(membershipType) }}
              </nz-tag>
            </td>
            <td>
              <div class="price-info">
                <span class="price">{{ plan.price }} MAD</span>
              </div>
            </td>
            <td>
              <span class="duration">{{ plan.duration }} jour(s)</span>
            </td>
            <td>
              <nz-tag [nzColor]="plan.isActive ? 'green' : 'red'">
                {{ plan.isActive ? 'Actif' : 'Inactif' }}
              </nz-tag>
            </td>
            <td>
              <div class="features-list">
                <nz-tag
                  *ngFor="let feature of plan.features.slice(0, 2)"
                  nzColor="blue"
                  class="feature-tag"
                >
                  {{ feature }}
                </nz-tag>
                <span *ngIf="plan.features.length > 2" class="more-features">
                  +{{ plan.features.length - 2 }} autres
                </span>
              </div>
            </td>
            <td>
              <div class="action-dropdown">
                <button
                  nz-button
                  nzType="text"
                  nzSize="small"
                  nz-dropdown
                  [nzDropdownMenu]="actionMenu"
                  nzPlacement="bottomRight"
                  class="action-trigger"
                >
                  <nz-icon nzType="more" nzTheme="outline"></nz-icon>
                </button>
                <nz-dropdown-menu #actionMenu="nzDropdownMenu">
                  <ul nz-menu>
                    <li nz-menu-item (click)="showPlanDetails(plan)">
                      <nz-icon nzType="eye" nzTheme="outline"></nz-icon>
                      <span>Voir détails</span>
                    </li>
                    <li nz-menu-item (click)="navigateToEdit(plan)">
                      <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
                      <span>Modifier</span>
                    </li>
                    <li nz-menu-divider></li>
                    <li
                      nz-menu-item
                      class="danger-item"
                      (click)="confirmDeletePlan(plan)"
                    >
                      <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                      <span>Supprimer</span>
                    </li>
                  </ul>
                </nz-dropdown-menu>
              </div>
            </td>
          </tr>
        </tbody>
        </nz-table>
      </div>
    </nz-spin>
  </nz-card>
</div>
