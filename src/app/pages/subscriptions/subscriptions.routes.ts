import { Routes } from '@angular/router';

export const SUBSCRIPTIONS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./subscriptions.component').then(m => m.SubscriptionsComponent)
  },
  {
    path: 'new',
    loadComponent: () => import('../subscription-form/subscription-form.component').then(m => m.SubscriptionFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('../subscription-form/subscription-form.component').then(m => m.SubscriptionFormComponent)
  },
  {
    path: 'details/:id',
    loadComponent: () => import('../subscription-details/subscription-details.component').then(m => m.SubscriptionDetailsComponent)
  }
];
