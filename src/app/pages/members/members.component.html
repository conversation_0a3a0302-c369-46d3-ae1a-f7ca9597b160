<div class="members-container">
  <!-- En-tête avec titre et bouton d'ajout -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="team" nzTheme="outline"></nz-icon>
        Gestion des membres
      </h1>
      <p class="page-subtitle"><PERSON><PERSON>rez les membres étudiants et professionnels de votre espace de coworking</p>
    </div>
    <button nz-button nzType="primary" nzSize="large" (click)="navigateToCreate()" class="add-button">
      <nz-icon nzType="plus"></nz-icon>
      Nouveau membre
    </button>
  </div>

  <!-- Filtres et recherche -->
  <nz-card class="filters-card" nzTitle="Filtres">
    <div class="filters-row">
      <div class="filter-item">
        <label>Rechercher</label>
        <nz-input-group nzPrefixIcon="search">
          <input
            nz-input
            placeholder="Nom, email, code étudiant..."
            (input)="onSearchChange($any($event.target).value)"
          />
        </nz-input-group>
      </div>

      <div class="filter-item">
        <label>Statut</label>
        <nz-select
          nzPlaceHolder="Tous les statuts"
          (ngModelChange)="onStatusFilterChange($event)"
          [ngModel]="'all'"
        >
          <nz-option
            *ngFor="let option of statusOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Type</label>
        <nz-select
          nzPlaceHolder="Tous les types"
          (ngModelChange)="onTypeFilterChange($event)"
          [ngModel]="'all'"
        >
          <nz-option
            *ngFor="let option of typeOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Abonnement</label>
        <nz-select
          nzPlaceHolder="Tous les abonnements"
          (ngModelChange)="onSubscriptionFilterChange($event)"
          [ngModel]="'all'"
        >
          <nz-option nzLabel="Tous" nzValue="all"></nz-option>
          <nz-option
            *ngFor="let subscription of subscriptions"
            [nzLabel]="subscription.name"
            [nzValue]="subscription.name"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>&nbsp;</label>
        <button
          nz-button
          nzType="default"
          (click)="clearFilters()"
          class="clear-filters-btn"
        >
          <nz-icon nzType="clear"></nz-icon>
          Effacer les filtres
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Tableau des membres -->
  <nz-card class="table-card">
    <nz-spin [nzSpinning]="isLoading()">
      <div class="table-wrapper">
        <nz-table
          #sortTable
          [nzData]="getFilteredMembers()"
          [nzPageSize]="10"
          [nzShowSizeChanger]="true"
          [nzPageSizeOptions]="[10, 20, 50]"
          nzShowQuickJumper
          [nzScroll]="{ x: '800px' }"
        >
        <thead>
          <tr>
            <th [nzSortFn]="listOfColumn[0].compare" [nzSortPriority]="listOfColumn[0].priority"
                nzLeft="0px" nzWidth="200px">
              {{ listOfColumn[0].title }}
            </th>
            <th [nzSortFn]="listOfColumn[1].compare" [nzSortPriority]="listOfColumn[1].priority" nzWidth="180px">
              {{ listOfColumn[1].title }}
            </th>
            <th [nzSortFn]="listOfColumn[2].compare" [nzSortPriority]="listOfColumn[2].priority" nzWidth="100px">
              {{ listOfColumn[2].title }}
            </th>
            <th [nzSortFn]="listOfColumn[3].compare" [nzSortPriority]="listOfColumn[3].priority" nzWidth="120px">
              {{ listOfColumn[3].title }}
            </th>
            <th [nzSortFn]="listOfColumn[4].compare" [nzSortPriority]="listOfColumn[4].priority" nzWidth="100px">
              {{ listOfColumn[4].title }}
            </th>
            <th [nzSortFn]="listOfColumn[5].compare" [nzSortPriority]="listOfColumn[5].priority" nzWidth="120px">
              {{ listOfColumn[5].title }}
            </th>
            <th [nzSortFn]="listOfColumn[6].compare" [nzSortPriority]="listOfColumn[6].priority" nzWidth="120px">
              {{ listOfColumn[6].title }}
            </th>
            <th nzWidth="80px">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let member of sortTable.data">
            <td nzLeft="0px">
              <div class="member-info">
                <div class="member-avatar">
                  {{ member.firstName.charAt(0) }}{{ member.lastName.charAt(0) }}
                </div>
                <div class="member-details">
                  <div class="member-name">
                    <a (click)="showMemberDetails(member)" style="color: inherit; text-decoration: none; cursor: pointer;">
                      {{ member.firstName }} {{ member.lastName }}
                    </a>
                  </div>
                  <div class="member-id">#{{ member.id }}</div>
                </div>
              </div>
            </td>
            <td>
              <div class="contact-info">
                <div class="email">{{ member.email }}</div>
                <div class="phone">{{ member.phone }}</div>
              </div>
            </td>
            <td>
              <nz-tag [nzColor]="getTypeColor(member.memberType)">
                {{ getTypeText(member.memberType) }}
              </nz-tag>
            </td>
            <td>{{ member.subscriptionType }}</td>
            <td>
              <nz-tag [nzColor]="getStatusColor(member.status)">
                {{ getStatusText(member.status) }}
              </nz-tag>
            </td>
            <td>
              <span *ngIf="member.studentCode" class="student-code">{{ member.studentCode }}</span>
              <span *ngIf="!member.studentCode" class="no-code">-</span>
            </td>
            <td>{{ getFormattedDate(member.createdAt) }}</td>
            <td>
              <div class="action-dropdown">
                <button
                  nz-button
                  nzType="text"
                  nzSize="small"
                  nz-dropdown
                  [nzDropdownMenu]="actionMenu"
                  nzPlacement="bottomRight"
                  class="action-trigger"
                >
                  <nz-icon nzType="more" nzTheme="outline"></nz-icon>
                </button>
                <nz-dropdown-menu #actionMenu="nzDropdownMenu">
                  <ul nz-menu>
                    <li nz-menu-item (click)="showMemberDetails(member)">
                      <nz-icon nzType="eye" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Voir détails</span>
                    </li>
                    <li nz-menu-item (click)="navigateToEdit(member)">
                      <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Modifier</span>
                    </li>
                    <li nz-menu-divider></li>
                    <li
                      nz-menu-item
                      class="danger-item"
                      (click)="confirmDeleteMember(member)"
                    >
                      <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Supprimer</span>
                    </li>
                  </ul>
                </nz-dropdown-menu>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>
      </div>
    </nz-spin>
  </nz-card>
</div>


