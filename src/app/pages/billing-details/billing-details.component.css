/* Page container */
.billing-details-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-button {
  height: 48px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  background: white !important;
  border: 2px solid #E5E5EA !important;
  color: #1C1C1E !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
}

.back-button:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-button {
  height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Share dropdown styles */
.ant-dropdown-menu-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.ant-dropdown-menu-item nz-icon {
  margin-right: 0 !important;
}

/* Content grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.invoice-card,
.client-card {
  grid-column: span 1;
}

.items-card,
.history-card {
  grid-column: span 2;
}

/* Cards */
.invoice-card,
.client-card,
.items-card,
.history-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.invoice-card .ant-card-head,
.client-card .ant-card-head,
.items-card .ant-card-head,
.history-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.invoice-card .ant-card-body,
.client-card .ant-card-body,
.items-card .ant-card-body,
.history-card .ant-card-body {
  padding: 24px !important;
}

/* Invoice info */
.invoice-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-row {
  display: flex;
  gap: 32px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.info-item label {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: 16px;
  color: #1C1C1E;
  font-weight: 600;
}

.status-tag {
  align-self: flex-start;
  font-weight: 500 !important;
  border-radius: 6px !important;
}

/* Client info */
.client-info {
  padding: 0;
}

.client-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.client-details {
  flex: 1;
}

.client-name {
  font-size: 20px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 4px 0;
  cursor: pointer;
  transition: color 0.2s ease;
}

.client-name:hover {
  color: #6E56CF;
}

.client-email {
  font-size: 14px;
  color: #8E8E93;
  margin: 0 0 4px 0;
}

.client-company {
  font-size: 14px;
  color: #6E56CF;
  font-weight: 500;
  margin: 0;
}

/* Items table */
.items-card .ant-table {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
}

.items-card .ant-table-thead > tr > th {
  background: #FAFAFA !important;
  border-bottom: 2px solid #E5E5EA !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
  padding: 16px !important;
}

.items-card .ant-table-tbody > tr > td {
  padding: 16px !important;
  border-bottom: 1px solid #F0F0F0 !important;
}

.item-description strong {
  color: #1C1C1E;
  font-size: 14px;
}

.item-type {
  font-size: 12px;
  color: #8E8E93;
  margin-top: 2px;
}

/* Totals */
.totals-section {
  border-top: 2px solid #E5E5EA;
  padding-top: 16px;
}

.totals-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.totals-row.total-final {
  border-top: 1px solid #E5E5EA;
  margin-top: 8px;
  padding-top: 16px;
  font-size: 18px;
  color: #1C1C1E;
}

/* Timeline */
.history-card .ant-timeline {
  margin-top: 16px;
}

.timeline-date {
  font-size: 12px;
  color: #8E8E93;
}

/* Loading and error states */
.loading-state,
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  margin-top: 16px;
  color: #8E8E93;
  font-size: 16px;
}

/* Responsive */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .invoice-card,
  .client-card,
  .items-card,
  .history-card {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .billing-details-container {
    padding: 0 20px;
  }

  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 20px 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .info-row {
    flex-direction: column;
    gap: 16px;
  }

  .client-header {
    flex-direction: column;
    text-align: center;
  }

  .action-button {
    width: 100%;
  }
}
