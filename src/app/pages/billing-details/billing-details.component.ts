import { Component, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';

import { Invoice } from '../../models/invoice.model';
import { BillingService } from '../../services/billing.service';
import { jsPDF } from 'jspdf';

@Component({
  selector: 'app-billing-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzTableModule,
    NzAvatarModule,
    NzTimelineModule,
    NzSpinModule,
    NzResultModule,
    NzModalModule,
    NzMessageModule,
    NzDropDownModule
  ],
  templateUrl: './billing-details.component.html',
  styleUrl: './billing-details.component.css'
})
export class BillingDetailsComponent implements OnInit {
  invoice: Invoice | null = null;
  loading = false;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private billingService: BillingService,
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  ngOnInit() {
    const invoiceId = this.route.snapshot.paramMap.get('id');
    if (invoiceId) {
      this.loadInvoice(invoiceId);
    } else {
      this.error = 'ID de facture manquant';
    }
  }

  private loadInvoice(id: string) {
    this.loading = true;
    this.billingService.getInvoices().subscribe({
      next: (invoices) => {
        this.invoice = invoices.find(inv => inv.id === id) || null;
        if (!this.invoice) {
          this.error = 'Facture non trouvée';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la facture:', error);
        this.error = 'Erreur lors du chargement de la facture';
        this.loading = false;
      }
    });
  }

  goBack() {
    this.location.back();
  }

  navigateToClient() {
    if (this.invoice) {
      this.router.navigate(['/members', this.invoice.memberId]);
    }
  }

  downloadPDF() {
    if (this.invoice) {
      this.generateInvoicePDF(this.invoice);
      this.message.success(`Facture ${this.invoice.invoiceNumber} téléchargée`);
    }
  }

  markAsPaid() {
    if (!this.invoice || this.invoice.status === 'paid') return;

    this.modal.confirm({
      nzTitle: 'Marquer comme payée',
      nzContent: `Voulez-vous marquer la facture <strong>${this.invoice.invoiceNumber}</strong> comme payée ?`,
      nzOkText: 'Confirmer',
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        if (this.invoice) {
          this.billingService.markAsPaid(this.invoice.id, 'card').subscribe({
            next: (updatedInvoice) => {
              this.invoice = updatedInvoice;
              this.message.success('Facture marquée comme payée');
            },
            error: (error) => {
              console.error('Erreur lors de la mise à jour:', error);
              this.message.error('Erreur lors de la mise à jour');
            }
          });
        }
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'draft': return 'default';
      case 'sent': return 'blue';
      case 'paid': return 'green';
      case 'overdue': return 'red';
      case 'cancelled': return 'orange';
      default: return 'default';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'draft': return 'Brouillon';
      case 'sent': return 'Envoyée';
      case 'paid': return 'Payée';
      case 'overdue': return 'En retard';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  }

  getPaymentMethodLabel(method?: string): string {
    if (!method) return 'N/A';
    switch (method) {
      case 'card': return 'Carte bancaire';
      case 'transfer': return 'Virement';
      case 'cash': return 'Espèces';
      case 'check': return 'Chèque';
      default: return method;
    }
  }

  getItemTypeLabel(type: string): string {
    switch (type) {
      case 'subscription': return 'Abonnement';
      case 'reservation': return 'Réservation';
      case 'service': return 'Service';
      case 'product': return 'Produit';
      default: return type;
    }
  }

  getClientInitials(): string {
    if (!this.invoice) return 'U';
    const names = this.invoice.memberName.split(' ');
    return names.map(name => name.charAt(0)).join('').toUpperCase();
  }

  private generateInvoicePDF(invoice: Invoice) {
    const doc = new jsPDF('p', 'mm', 'a4');

    // Configuration
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    // En-tête
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('WORKEEM', margin, yPosition);

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Espace de coworking moderne', margin, yPosition + 8);
    doc.text('Technopolis, Hay Riad, Rabat, Maroc', margin, yPosition + 16);
    doc.text('<EMAIL> | +212 5 37 12 34 56', margin, yPosition + 24);

    // Ligne de séparation
    yPosition += 40;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Titre du reçu
    yPosition += 20;
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('FACTURE', margin, yPosition);

    // Informations de la facture
    yPosition += 20;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Numéro: ${invoice.invoiceNumber}`, margin, yPosition);
    doc.text(`Date d'émission: ${invoice.issueDate.toLocaleDateString('fr-FR')}`, margin, yPosition + 8);
    doc.text(`Date d'échéance: ${invoice.dueDate.toLocaleDateString('fr-FR')}`, margin, yPosition + 16);
    doc.text(`Statut: ${this.getStatusLabel(invoice.status)}`, margin, yPosition + 24);

    if (invoice.paidDate) {
      doc.text(`Date de paiement: ${invoice.paidDate.toLocaleDateString('fr-FR')}`, margin, yPosition + 32);
      yPosition += 8;
    }

    // Informations du client
    yPosition += 50;
    doc.setFont('helvetica', 'bold');
    doc.text('Facturé à:', margin, yPosition);

    yPosition += 15;
    doc.setFont('helvetica', 'normal');
    doc.text(invoice.memberName, margin, yPosition);
    doc.text(invoice.memberEmail, margin, yPosition + 8);
    if (invoice.memberCompany) {
      doc.text(invoice.memberCompany, margin, yPosition + 16);
      yPosition += 8;
    }

    // Tableau des items
    yPosition += 30;
    doc.setFont('helvetica', 'bold');
    doc.text('Description', margin, yPosition);
    doc.text('Qté', margin + 100, yPosition);
    doc.text('Prix unitaire', margin + 120, yPosition);
    doc.text('Total', margin + 160, yPosition);

    // Ligne sous les en-têtes
    yPosition += 5;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Items
    yPosition += 10;
    doc.setFont('helvetica', 'normal');

    invoice.items.forEach((item) => {
      doc.text(item.description, margin, yPosition);
      doc.text(item.quantity.toString(), margin + 100, yPosition);
      doc.text(`${item.unitPrice.toFixed(2)} MAD`, margin + 120, yPosition);
      doc.text(`${item.total.toFixed(2)} MAD`, margin + 160, yPosition);
      yPosition += 12;
    });

    // Totaux
    yPosition += 5;
    doc.line(margin + 120, yPosition, pageWidth - margin, yPosition);

    yPosition += 8;
    doc.text(`Sous-total: ${invoice.subtotal.toFixed(2)} MAD`, margin + 120, yPosition);
    yPosition += 6;
    doc.text(`TVA (${invoice.taxRate}%): ${invoice.taxAmount.toFixed(2)} MAD`, margin + 120, yPosition);

    yPosition += 10;
    doc.setFont('helvetica', 'bold');
    doc.text(`TOTAL: ${invoice.total.toFixed(2)} MAD`, margin + 120, yPosition);

    // Vérifier s'il y a assez d'espace pour le pied de page
    const footerHeight = 40;
    const availableSpace = doc.internal.pageSize.height - yPosition - footerHeight;

    // Si pas assez d'espace, ajuster la position
    let footerY;
    if (availableSpace < 20) {
      footerY = doc.internal.pageSize.height - 25;
    } else {
      footerY = Math.max(yPosition + 30, doc.internal.pageSize.height - 35);
    }

    // Ligne de séparation du pied de page
    doc.line(margin, footerY - 8, pageWidth - margin, footerY - 8);

    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    doc.text('Merci de votre confiance !', margin, footerY);

    // Date de génération alignée à droite
    const dateText = `Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`;
    const dateWidth = doc.getTextWidth(dateText);
    doc.text(dateText, pageWidth - margin - dateWidth, footerY);

    // Télécharger le PDF
    try {
      const pdfBlob = doc.output('blob');
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `facture-${invoice.invoiceNumber}.pdf`;
      link.click();
      window.URL.revokeObjectURL(url);
      this.message.success('PDF téléchargé avec succès');
    } catch (error) {
      console.error('Erreur lors de la génération du PDF:', error);
      this.message.error('Erreur lors de la génération du PDF');
      // Fallback vers la méthode save() standard
      doc.save(`facture-${invoice.invoiceNumber}.pdf`);
    }
  }

  // Méthodes de partage
  shareViaWhatsApp() {
    if (!this.invoice) return;

    // Générer le PDF en blob pour le partage
    this.generatePDFBlob(this.invoice).then(blob => {
      // Créer un message WhatsApp avec les détails de la facture
      const message = `Bonjour ${this.invoice!.memberName},\n\nVeuillez trouver ci-joint votre facture ${this.invoice!.invoiceNumber} d'un montant de ${this.invoice!.total.toFixed(2)} MAD.\n\nDate d'échéance: ${this.invoice!.dueDate.toLocaleDateString('fr-FR')}\n\nCordialement,\nÉquipe WORKEEM`;

      // Encoder le message pour l'URL
      const encodedMessage = encodeURIComponent(message);

      // Télécharger le PDF avec un nom spécifique
      this.downloadPDFBlob(blob, `facture-${this.invoice!.invoiceNumber}.pdf`);

      // Attendre un peu pour que le téléchargement commence
      setTimeout(() => {
        // Ouvrir WhatsApp Web avec le message pré-rempli
        const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');

        // Afficher des instructions claires à l'utilisateur
        this.message.info('PDF téléchargé ! Glissez-déposez le fichier dans WhatsApp pour l\'envoyer.', { nzDuration: 8000 });
      }, 1000);
    });
  }

  shareViaEmail() {
    if (!this.invoice) return;

    // Générer le PDF en blob
    this.generatePDFBlob(this.invoice).then(blob => {
      // Télécharger le PDF d'abord
      this.downloadPDFBlob(blob, `facture-${this.invoice!.invoiceNumber}.pdf`);

      // Préparer l'email avec les détails de la facture
      const subject = `Facture ${this.invoice!.invoiceNumber} - WORKEEM`;
      const body = `Bonjour ${this.invoice!.memberName},

Veuillez trouver ci-joint votre facture ${this.invoice!.invoiceNumber}.

Détails de la facture:
- Numéro: ${this.invoice!.invoiceNumber}
- Date d'émission: ${this.invoice!.issueDate.toLocaleDateString('fr-FR')}
- Date d'échéance: ${this.invoice!.dueDate.toLocaleDateString('fr-FR')}
- Montant total: ${this.invoice!.total.toFixed(2)} MAD
- Statut: ${this.getStatusLabel(this.invoice!.status)}

Merci de procéder au paiement avant la date d'échéance.

Cordialement,
Équipe WORKEEM
<EMAIL>
+212 5 37 12 34 56`;

      // Attendre que le téléchargement commence
      setTimeout(() => {
        // Essayer d'utiliser l'API Web Share si disponible (pour mobile)
        if (navigator.share && navigator.canShare && navigator.canShare({ files: [new File([blob], `facture-${this.invoice!.invoiceNumber}.pdf`, { type: 'application/pdf' })] })) {
          const file = new File([blob], `facture-${this.invoice!.invoiceNumber}.pdf`, { type: 'application/pdf' });
          navigator.share({
            title: subject,
            text: body,
            files: [file]
          }).then(() => {
            this.message.success('Facture partagée avec succès !');
          }).catch((error) => {
            console.error('Erreur lors du partage:', error);
            this.fallbackEmailShare(this.invoice!, subject, body);
          });
        } else {
          // Fallback pour desktop
          this.fallbackEmailShare(this.invoice!, subject, body);
        }
      }, 1000);
    });
  }

  private fallbackEmailShare(invoice: Invoice, subject: string, body: string) {
    // Encoder les paramètres pour l'URL mailto
    const encodedSubject = encodeURIComponent(subject);
    const encodedBody = encodeURIComponent(body);
    const encodedEmail = encodeURIComponent(invoice.memberEmail);

    // Créer l'URL mailto
    const mailtoUrl = `mailto:${encodedEmail}?subject=${encodedSubject}&body=${encodedBody}`;

    // Ouvrir le client email par défaut
    window.location.href = mailtoUrl;

    // Afficher des instructions claires
    this.message.info('PDF téléchargé ! Attachez le fichier à votre email avant d\'envoyer.', { nzDuration: 8000 });
  }

  private async generatePDFBlob(invoice: Invoice): Promise<Blob> {
    // Utiliser la même logique que generateInvoicePDF mais retourner un blob
    const doc = new jsPDF();

    // Configuration
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    // En-tête de l'entreprise
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('WORKEEM', margin, yPosition);

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Espace de coworking moderne', margin, yPosition + 8);
    doc.text('Technopolis, Hay Riad, Rabat, Maroc', margin, yPosition + 16);
    doc.text('<EMAIL> | +212 5 37 12 34 56', margin, yPosition + 24);

    // Informations de la facture (côté droit)
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(`FACTURE ${invoice.invoiceNumber}`, pageWidth - margin - 60, yPosition);

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Date d'émission: ${invoice.issueDate.toLocaleDateString('fr-FR')}`, pageWidth - margin - 60, yPosition + 15);
    doc.text(`Date d'échéance: ${invoice.dueDate.toLocaleDateString('fr-FR')}`, pageWidth - margin - 60, yPosition + 25);

    // Ligne de séparation
    yPosition += 50;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Informations client
    yPosition += 20;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Facturé à:', margin, yPosition);

    doc.setFont('helvetica', 'normal');
    doc.text(invoice.memberName, margin, yPosition + 15);
    doc.text(invoice.memberEmail, margin, yPosition + 25);
    if (invoice.memberCompany) {
      doc.text(invoice.memberCompany, margin, yPosition + 35);
      yPosition += 10;
    }

    // Tableau des articles
    yPosition += 50;
    doc.setFont('helvetica', 'bold');
    doc.text('Description', margin, yPosition);
    doc.text('Quantité', margin + 80, yPosition);
    doc.text('Prix unitaire', margin + 120, yPosition);
    doc.text('Total', pageWidth - margin - 30, yPosition);

    // Ligne sous les en-têtes
    yPosition += 5;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Articles
    yPosition += 15;
    doc.setFont('helvetica', 'normal');
    invoice.items.forEach(item => {
      doc.text(item.description, margin, yPosition);
      doc.text(item.quantity.toString(), margin + 80, yPosition);
      doc.text(`${item.unitPrice.toFixed(2)} MAD`, margin + 120, yPosition);
      doc.text(`${item.total.toFixed(2)} MAD`, pageWidth - margin - 30, yPosition, { align: 'right' });
      yPosition += 15;
    });

    // Ligne de séparation avant totaux
    yPosition += 10;
    doc.line(margin + 80, yPosition, pageWidth - margin, yPosition);

    // Totaux
    yPosition += 15;
    doc.text(`Sous-total: ${invoice.subtotal.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);
    yPosition += 10;
    doc.text(`TVA (${invoice.taxRate}%): ${invoice.taxAmount.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);
    yPosition += 10;
    doc.setFont('helvetica', 'bold');
    doc.text(`TOTAL: ${invoice.total.toFixed(2)} MAD`, pageWidth - margin - 60, yPosition);

    // Statut de paiement
    yPosition += 20;
    doc.setFont('helvetica', 'normal');
    const statusText = invoice.status === 'paid' ? 'PAYÉE' :
                      invoice.status === 'overdue' ? 'EN RETARD' :
                      invoice.status === 'sent' ? 'ENVOYÉE' : 'BROUILLON';
    doc.text(`Statut: ${statusText}`, margin, yPosition);

    if (invoice.paidDate) {
      doc.text(`Date de paiement: ${invoice.paidDate.toLocaleDateString('fr-FR')}`, margin, yPosition + 10);
    }

    // Notes
    if (invoice.notes) {
      yPosition += 30;
      doc.setFont('helvetica', 'bold');
      doc.text('Notes:', margin, yPosition);
      doc.setFont('helvetica', 'normal');
      doc.text(invoice.notes, margin, yPosition + 10);
    }

    // Pied de page
    yPosition = doc.internal.pageSize.height - 40;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    yPosition += 15;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    doc.text('Merci de votre confiance !', margin, yPosition);
    doc.text(`Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`,
             pageWidth - margin - 80, yPosition);

    return doc.output('blob');
  }

  private downloadPDFBlob(blob: Blob, filename: string) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Méthode de partage par lien (même logique que dans billing.component)
  async shareViaLink() {
    if (!this.invoice) return;

    try {
      this.message.loading('Création du lien de partage...', { nzDuration: 0 });

      const shareUrl = await this.createTemporaryShareLink(this.invoice);

      // Copier le lien dans le presse-papiers
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareUrl);
        this.message.remove();
        this.message.success('Lien de partage copié dans le presse-papiers ! Valide 24h.', { nzDuration: 5000 });
      } else {
        // Fallback pour les navigateurs plus anciens
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.message.remove();
        this.message.success('Lien de partage copié ! Valide 24h.', { nzDuration: 5000 });
      }

    } catch (error) {
      this.message.remove();
      this.message.error('Erreur lors de la création du lien de partage');
      console.error('Erreur partage:', error);
    }
  }

  private createTemporaryShareLink(invoice: Invoice): Promise<string> {
    return new Promise((resolve) => {
      // Simulation d'un service de partage temporaire
      const shareId = this.generateShareId();
      const shareUrl = `${window.location.origin}/shared/invoice/${shareId}`;

      // Stocker temporairement dans localStorage
      const shareData = {
        invoiceId: invoice.id,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 heures
        accessCount: 0,
        maxAccess: 5
      };
      localStorage.setItem(`share_${shareId}`, JSON.stringify(shareData));

      setTimeout(() => resolve(shareUrl), 500);
    });
  }

  private generateShareId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }



  getFormattedDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getFormattedDateTime(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }
}
