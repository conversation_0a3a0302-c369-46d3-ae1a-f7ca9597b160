/* Page container */
.reservation-form-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.cancel-button {
  height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  background: white !important;
  border: 2px solid #E5E5EA !important;
  color: #1C1C1E !important;
}

.cancel-button:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
}

/* Form card */
.form-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
  background: white;
}

.form-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.form-card .ant-card-body {
  padding: 32px !important;
}

/* Form container */
.reservation-form {
  padding: 0;
}

/* Form items */
.ant-form-item {
  margin-bottom: 20px !important;
}

.ant-form-item-label > label {
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.ant-form-item-control-input {
  min-height: 40px !important;
}

/* Input styles */
.ant-input,
.ant-select-selector,
.ant-input-number,
.ant-picker {
  border-radius: 6px !important;
  border-color: #E5E5EA !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-input-number-focused,
.ant-picker-focused {
  border-color: #6E56CF !important;
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.1) !important;
}

/* Space option in select */
.space-option {
  padding: 4px 0;
}

.space-name {
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 2px;
}

.space-details {
  font-size: 12px;
  color: #8E8E93;
}

/* Member option in select */
.member-option {
  padding: 4px 0;
}

.member-name {
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 2px;
}

.member-details {
  font-size: 12px;
  color: #8E8E93;
}

/* Client type radio */
.ant-radio-group {
  display: flex;
  gap: 24px;
}

.ant-radio-wrapper {
  font-size: 14px;
  color: #1C1C1E;
}

/* New client section */
h3 {
  border-bottom: 1px solid #E5E5EA;
  padding-bottom: 8px;
}

/* Capacity info */
.capacity-info {
  margin-top: 4px;
}

.capacity-info small {
  color: #8E8E93;
  font-size: 12px;
}

/* Space info card */
.space-info-card {
  margin: 20px 0;
  border-radius: 8px !important;
  border: 1px solid #E5E5EA !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
}

.space-info-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 12px 16px !important;
}

.space-info-card .ant-card-body {
  padding: 16px !important;
}

.space-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #1C1C1E;
}

.info-item nz-icon {
  color: #6E56CF;
  font-size: 16px;
}

.equipment-list h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 8px 0;
}

.equipment-list .ant-tag {
  margin-bottom: 4px;
  border-radius: 4px !important;
  font-size: 12px !important;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 0 0 0;
  border-top: 1px solid #E5E5EA;
  margin-top: 20px;
}

.form-actions button {
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
}

/* Responsive */
@media (max-width: 768px) {
  .space-info {
    flex-direction: column;
    gap: 12px;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}

/* New client section */
.new-client-section {
  margin: 20px 0;
}

.new-client-card {
  background: linear-gradient(135deg, #F8F9FF 0%, #F0F4FF 100%) !important;
  border: 1px solid #E8EEFF !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(110, 86, 207, 0.08) !important;
}

.new-client-card .ant-card-head {
  background: transparent !important;
  border-bottom: 1px solid #E8EEFF !important;
}

.new-client-card .ant-card-head-title {
  color: #6E56CF !important;
  font-weight: 600 !important;
}

/* Styles pour la durée */
.duration-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.duration-group {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 120px;
}

.duration-input {
  width: 80px;
}

.duration-select {
  width: 90px;
}

.duration-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.duration-total {
  margin-left: 8px;
  padding: 8px 12px;
  background: #F0F8FF;
  border-radius: 6px;
  border: 1px solid #E1F5FE;
  flex: 1;
  min-width: 200px;
}

.total-label {
  font-size: 14px;
  font-weight: 500;
  color: #6E56CF;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .reservation-form-container {
    padding: 0 16px;
  }

  .page-header {
    padding: 20px 0;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .form-card .ant-card-body {
    padding: 16px !important;
  }

  /* Durée responsive */
  .duration-inputs {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .duration-group {
    justify-content: space-between;
    min-width: auto;
    width: 100%;
  }

  .duration-input,
  .duration-select {
    flex: 1;
    max-width: 120px;
  }

  .duration-total {
    margin-left: 0;
    margin-top: 8px;
    min-width: auto;
    text-align: center;
  }

  /* Client form responsive */
  .new-client-card .ant-card-body {
    padding: 16px !important;
  }

  /* Nouveau client - tous les champs en colonne sur mobile */
  .new-client-form [nz-row] {
    flex-direction: column !important;
  }

  .new-client-form [nz-col] {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    margin-bottom: 16px !important;
  }

  /* Heures et nombre de personnes responsive */
  .reservation-form [nz-row]:has([nz-col][nzSpan="8"]) {
    flex-direction: column;
  }

  .reservation-form [nz-col][nzSpan="8"] {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    margin-bottom: 16px;
  }

  /* Objet et récurrence responsive */
  .reservation-form [nz-col][nzSpan="16"],
  .reservation-form [nz-col][nzSpan="8"] {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    margin-bottom: 16px;
  }

  /* Form actions responsive */
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }

  .form-actions button {
    width: 100% !important;
    justify-content: center !important;
  }
}

@media (max-width: 480px) {
  .duration-group {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .duration-input,
  .duration-select {
    max-width: none;
    width: 100%;
  }

  .duration-label {
    text-align: center;
    font-weight: 500;
  }
}

.total-label {
  font-size: 13px;
  color: #6E56CF;
  font-weight: 500;
}
