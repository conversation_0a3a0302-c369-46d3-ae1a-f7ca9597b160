/* Page container */
.spaces-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.add-button,
.calendar-button {
  height: 48px !important;
  min-height: 48px !important;
  max-height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  min-width: 140px !important;
  justify-content: center !important;
  line-height: 1 !important;
}

/* Force la hauteur pour les boutons Ant Design */
.header-actions .ant-btn-lg {
  height: 48px !important;
  min-height: 48px !important;
  max-height: 48px !important;
  line-height: 46px !important;
}

.add-button {
  background: #6E56CF !important;
  border: none !important;
  color: white !important;
}

.add-button:hover {
  background: #5A47B8 !important;
  color: white !important;
  transform: translateY(-1px);
}

.calendar-button {
  background: white !important;
  border: 2px solid #E5E5EA !important;
  color: #1C1C1E !important;
}

.calendar-button:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  transform: translateY(-1px);
}

/* Stats section */
.stats-section {
  margin-bottom: 24px;
  padding: 0;
  background: transparent;
  border-bottom: none;
}

.stat-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #f0f0f0 !important;
  transition: all 0.3s ease;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-2px);
}

.stat-card .ant-card-body {
  padding: 20px !important;
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
}

.stat-content {
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
  width: 100% !important;
}

.stat-icon {
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 20px !important;
  flex-shrink: 0 !important;
}

.stat-icon.total-spaces {
  background: linear-gradient(135deg, #6E56CF, #8B7ED8) !important;
  color: white !important;
}

.stat-icon.available-spaces {
  background: linear-gradient(135deg, #52C41A, #73D13D) !important;
  color: white !important;
}

.stat-icon.occupied-spaces {
  background: linear-gradient(135deg, #FA8C16, #FFA940) !important;
  color: white !important;
}

.stat-icon.total-capacity {
  background: linear-gradient(135deg, #1890FF, #40A9FF) !important;
  color: white !important;
}

.stat-info {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
}

.stat-title {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #8E8E93 !important;
  margin: 0 !important;
}

.stat-value {
  font-size: 24px !important;
  font-weight: 700 !important;
  color: #1C1C1E !important;
  margin: 0 !important;
  line-height: 1 !important;
}

.stat-card .ant-card-body {
  padding: 16px !important;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total-spaces {
  background: linear-gradient(135deg, #6E56CF, #8B7ED8);
}

.stat-icon.available-spaces {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-icon.occupied-spaces {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.stat-icon.total-capacity {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1C1C1E;
  line-height: 1;
}

.stat-value.available {
  color: #52c41a;
}

.stat-value.occupied {
  color: #ff4d4f;
}

/* Filters */
.filters-card {
  margin-bottom: 24px;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
  background: white;
}

.filters-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.filters-card .ant-card-body {
  padding: 24px !important;
}

.filters-row {
  display: flex;
  gap: 24px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.filter-item label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

.filter-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-left: auto;
}

.filter-actions button {
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

/* Spaces grid */
.spaces-content {
  background: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  min-height: calc(100vh - 300px);
}

.spaces-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.space-card {
  height: 100%;
}

.space-item {
  height: 100%;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #E5E5EA !important;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  min-height: 400px;
}

.space-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
}

.space-item .ant-card-body {
  padding: 20px 20px 70px 20px !important;
}

.space-item .ant-card-actions {
  border-radius: 0 0 12px 12px !important;
  background: white !important;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 !important;
  border-top: 1px solid #E5E5EA !important;
}

/* Space header */
.space-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.space-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.space-info {
  flex: 1;
  min-width: 0;
}

.space-name {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 4px 0;
  cursor: pointer;
  transition: color 0.2s ease;
}

.space-name:hover {
  color: #6E56CF;
}

.space-location {
  font-size: 14px;
  color: #8E8E93;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.space-status {
  flex-shrink: 0;
}

/* Space content */
.space-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.space-description {
  font-size: 14px;
  color: #8E8E93;
  line-height: 1.5;
  margin: 0;
}

.space-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #1C1C1E;
  font-weight: 500;
}

.detail-item nz-icon {
  color: #6E56CF;
}

.space-type {
  display: flex;
  justify-content: flex-start;
}

.space-equipment,
.space-amenities {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.space-equipment h4,
.space-amenities h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

.equipment-list,
.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.equipment-tag {
  font-size: 12px !important;
  border-radius: 4px !important;
}

.equipment-more {
  font-size: 12px !important;
  border-radius: 4px !important;
}

.amenity-item {
  font-size: 12px;
  color: #8E8E93;
  background-color: #F0F0F0;
  padding: 2px 8px;
  border-radius: 4px;
}

.amenities-more {
  font-size: 12px;
  color: #8E8E93;
  font-style: italic;
}

/* Actions */
.ant-card-actions {
  border-top: 1px solid #E5E5EA !important;
  border-radius: 0 0 12px 12px !important;
  background: white !important;
  margin: 0 !important;
}

.ant-card-actions > li {
  margin: 8px 0 !important;
}

.ant-card-actions > li > span {
  color: #8E8E93 !important;
  font-size: 16px !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;
}

.ant-card-actions > li > span:hover {
  color: #6E56CF !important;
}

/* Responsive */
@media (max-width: 1200px) {
  .spaces-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 992px) {
  .spaces-container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 26px;
  }

  .header-actions {
    flex-direction: row;
    gap: 12px;
  }

  .header-actions button {
    flex: 1;
    min-width: 120px;
  }

  .filters-row {
    flex-wrap: wrap;
    gap: 16px;
  }

  .filter-item {
    min-width: 180px;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .spaces-container {
    padding: 0 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: left;
    padding: 16px 0;
  }

  .header-content {
    text-align: left;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
    flex-direction: column;
    gap: 12px;
  }

  .header-actions .ant-btn {
    height: 48px !important;
    min-height: 48px !important;
    max-height: 48px !important;
  }

  .add-button,
  .calendar-button {
    width: 100% !important;
    justify-content: center !important;
    height: 48px !important;
    min-height: 48px !important;
    max-height: 48px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    padding: 0 24px !important;
    line-height: 48px !important;
  }

  /* Stats responsive */
  .stats-section [nz-col] {
    margin-bottom: 12px;
  }

  .stat-card {
    margin-bottom: 0 !important;
  }

  .stat-content {
    padding: 12px !important;
  }

  .stat-icon {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
  }

  .stat-title {
    font-size: 12px !important;
  }

  .stat-value {
    font-size: 20px !important;
  }

  /* Filtres responsive */
  .filters-card {
    margin-bottom: 16px;
  }

  .filters-card .ant-card-body {
    padding: 16px !important;
  }

  .filters-row {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-item {
    min-width: auto;
    flex: none;
    width: 100%;
  }

  .filter-item label {
    font-size: 14px;
    margin-bottom: 6px;
    display: block;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: stretch;
    width: 100%;
    flex-direction: row;
    gap: 12px;
    margin-top: 8px;
  }

  .filter-actions button {
    flex: 1 !important;
    justify-content: center !important;
    height: 44px !important;
    font-size: 14px !important;
  }

  .spaces-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .space-item {
    min-height: 350px;
  }

  .space-item .ant-card-body {
    padding: 16px 16px 60px 16px !important;
  }

  .space-header {
    flex-direction: row;
    align-items: flex-start;
    text-align: left;
    gap: 12px;
  }

  .space-icon {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
  }

  .space-name {
    font-size: 16px !important;
    line-height: 1.3;
  }

  .space-location {
    font-size: 12px !important;
  }

  .space-details {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 8px;
  }

  .space-detail {
    font-size: 11px !important;
    padding: 4px 8px !important;
  }

  .space-amenities h4 {
    font-size: 13px !important;
  }

  .amenity-item {
    font-size: 10px !important;
    padding: 2px 6px !important;
  }
}

/* Tags personnalisés */
.ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  border: none !important;
  font-size: 12px !important;
  padding: 2px 8px !important;
  line-height: 1.4 !important;
}

/* Empty state */
.ant-empty {
  padding: 60px 20px !important;
}

.ant-empty-description {
  color: #8E8E93 !important;
  font-size: 16px !important;
}

/* Statistics */
.ant-statistic-title {
  font-size: 14px !important;
  color: #8E8E93 !important;
  font-weight: 500 !important;
}

.ant-statistic-content {
  font-size: 24px !important;
  font-weight: 700 !important;
  color: #1C1C1E !important;
}
