<div class="billing-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
        Facturation & Paiements
      </h1>
      <p class="page-description">Gérez vos factures, paiements et générez automatiquement la facturation mensuelle</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" nzSize="large" (click)="generateMonthlyInvoices()" class="generate-button">
        <nz-icon nzType="calendar"></nz-icon>
        Générer factures mensuelles
      </button>
      <button nz-button nzType="primary" nzSize="large" (click)="createNewInvoice()" class="add-button">
        <nz-icon nzType="plus"></nz-icon>
        Nouvelle facture
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <div class="stats-grid">
      <nz-card class="stat-card revenue">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="rise" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.totalRevenue | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">Chiffre d'affaires total</div>
          </div>
        </div>
      </nz-card>

      <nz-card class="stat-card monthly">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.monthlyRevenue | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">Revenus ce mois</div>
          </div>
        </div>
      </nz-card>

      <nz-card class="stat-card pending">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="clock-circle" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.pendingAmount | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">En attente ({{ stats.pendingInvoices }})</div>
          </div>
        </div>
      </nz-card>

      <nz-card class="stat-card overdue">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-icon nzType="exclamation-circle" nzTheme="outline"></nz-icon>
          </div>
          <div class="stat-details">
            <div class="stat-value">{{ stats.overdueAmount | currency:'MAD ':'symbol':'1.0-0' }}</div>
            <div class="stat-label">En retard ({{ stats.overdueInvoices }})</div>
          </div>
        </div>
      </nz-card>
    </div>
  </div>

  <!-- Filtres et actions -->
  <nz-card class="filters-card" nzTitle="Filtres et actions">
    <div class="filters-row">
      <div class="filter-item">
        <label>Statut</label>
        <nz-select
          [(ngModel)]="selectedStatus"
          nzPlaceHolder="Tous les statuts"
          nzAllowClear
          (ngModelChange)="onStatusFilterChange($event)"
        >
          <nz-option nzLabel="Brouillon" nzValue="draft"></nz-option>
          <nz-option nzLabel="Envoyée" nzValue="sent"></nz-option>
          <nz-option nzLabel="Payée" nzValue="paid"></nz-option>
          <nz-option nzLabel="En retard" nzValue="overdue"></nz-option>
          <nz-option nzLabel="Annulée" nzValue="cancelled"></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Période</label>
        <nz-range-picker
          [(ngModel)]="dateRange"
          nzFormat="dd/MM/yyyy"
          (ngModelChange)="onDateRangeChange($event)"
        ></nz-range-picker>
      </div>

      <div class="filter-item">
        <label>Client</label>
        <nz-input-group nzSearch nzEnterButton="Rechercher">
          <input
            type="text"
            nz-input
            placeholder="Nom ou email du client"
            [(ngModel)]="searchTerm"
            (ngModelChange)="onSearchChange($event)"
          />
        </nz-input-group>
      </div>

      <div class="filter-actions">
        <button nz-button nzType="default" (click)="exportToCSV()">
          <nz-icon nzType="download"></nz-icon>
          Export CSV
        </button>
        <button nz-button nzType="default" (click)="exportToPDF()">
          <nz-icon nzType="file-pdf"></nz-icon>
          Export PDF
        </button>
        <button nz-button nzType="default" (click)="clearFilters()">
          <nz-icon nzType="clear"></nz-icon>
          Effacer
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Liste des factures -->
  <nz-card class="invoices-card" nzTitle="Factures">
    <div class="table-container">
      <div class="table-wrapper">
        <nz-table
          #invoicesTable
          [nzData]="filteredInvoices"
          [nzLoading]="loading"
          [nzPageSize]="10"
          [nzShowSizeChanger]="true"
          [nzShowQuickJumper]="true"
          [nzShowTotal]="totalTemplate"
          [nzScroll]="{ x: '900px' }"
        >
        <thead>
          <tr>
            <th nzSortKey="invoiceNumber" nzWidth="120px">Numéro</th>
            <th nzSortKey="memberName" nzLeft="0px" nzWidth="200px">Client</th>
            <th nzSortKey="issueDate" nzWidth="120px">Date émission</th>
            <th nzSortKey="dueDate" nzWidth="120px">Date échéance</th>
            <th nzSortKey="status" nzWidth="100px">Statut</th>
            <th nzSortKey="total" nzWidth="120px">Montant</th>
            <th nzWidth="120px">Actions</th>
          </tr>
        </thead>
      <tbody>
        <tr *ngFor="let invoice of invoicesTable.data">
          <td>
            <strong class="clickable-invoice-number" (click)="viewInvoice(invoice)">
              {{ invoice.invoiceNumber }}
            </strong>
          </td>
          <td nzLeft="0px">
            <div class="client-info" (click)="navigateToMemberDetail(invoice.memberId)" class="clickable-client">
              <div class="client-name">{{ invoice.memberName }}</div>
              <div class="client-email">{{ invoice.memberEmail }}</div>
              <div class="client-company" *ngIf="invoice.memberCompany">{{ invoice.memberCompany }}</div>
            </div>
          </td>
          <td>{{ invoice.issueDate | date:'dd/MM/yyyy' }}</td>
          <td>{{ invoice.dueDate | date:'dd/MM/yyyy' }}</td>
          <td>
            <nz-tag [nzColor]="getStatusColor(invoice.status)">
              {{ getStatusLabel(invoice.status) }}
            </nz-tag>
          </td>
          <td>
            <div class="amount-info">
              <div class="total-amount">{{ invoice.total | currency:'MAD ':'symbol':'1.2-2' }}</div>
            </div>
          </td>
          <td>
            <div class="action-buttons">
              <!-- Action : Marquer comme payée (toujours visible) -->
              <button
                nz-button
                nzType="text"
                nzSize="small"
                (click)="markAsPaid(invoice)"
                [nz-tooltip]="invoice.status === 'paid' ? 'Déjà payée' : 'Marquer comme payée'"
                [disabled]="invoice.status === 'paid'"
              >
                <nz-icon nzType="check-circle" nzTheme="outline"></nz-icon>
              </button>

              <!-- Menu dropdown pour les autres actions -->
              <div class="action-dropdown">
                <button
                  nz-button
                  nzType="text"
                  nzSize="small"
                  nz-dropdown
                  [nzDropdownMenu]="actionMenu"
                  nzPlacement="bottomRight"
                  class="action-trigger"
                >
                  <nz-icon nzType="more" nzTheme="outline"></nz-icon>
                </button>
                <nz-dropdown-menu #actionMenu="nzDropdownMenu">
                  <ul nz-menu>
                    <li nz-menu-item (click)="viewInvoice(invoice)">
                      <nz-icon nzType="eye" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Voir détails</span>
                    </li>
                    <li nz-menu-item (click)="editInvoice(invoice)">
                      <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Modifier</span>
                    </li>
                    <li nz-menu-item (click)="downloadPDF(invoice)">
                      <nz-icon nzType="download" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Télécharger PDF</span>
                    </li>
                    <li nz-menu-divider></li>
                    <li nz-menu-item (click)="shareViaLink(invoice)">
                      <nz-icon nzType="link" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Lien de partage</span>
                    </li>
                    <li nz-menu-item (click)="shareViaWhatsApp(invoice)">
                      <nz-icon nzType="whats-app" nzTheme="outline"></nz-icon>
                      <span class="ml-1">WhatsApp</span>
                    </li>
                    <li nz-menu-item (click)="shareViaEmail(invoice)">
                      <nz-icon nzType="mail" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Email</span>
                    </li>
                    <li nz-menu-divider></li>
                    <li nz-menu-item class="danger-item" (click)="deleteInvoice(invoice)">
                      <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Supprimer</span>
                    </li>
                  </ul>
                </nz-dropdown-menu>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
        </nz-table>
      </div>

      <ng-template #totalTemplate let-total>
        Total: {{ total }} facture(s)
      </ng-template>
    </div>
  </nz-card>
</div>
