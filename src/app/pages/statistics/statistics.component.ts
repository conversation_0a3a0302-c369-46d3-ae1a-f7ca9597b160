import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzProgressModule } from 'ng-zorro-antd/progress';

@Component({
  selector: 'app-statistics',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzStatisticModule,
    NzIconModule,
    NzGridModule,
    NzProgressModule
  ],
  templateUrl: './statistics.component.html',
  styleUrls: ['./statistics.component.css']
})
export class StatisticsComponent implements OnInit {

  // Données pour les graphiques interactifs
  monthlyData = [
    { month: 'Jan', paid: 15420, unpaid: 3240, total: 18660 },
    { month: 'Fév', paid: 18650, unpaid: 2890, total: 21540 },
    { month: 'Mar', paid: 22340, unpaid: 4120, total: 26460 },
    { month: 'Avr', paid: 19870, unpaid: 3560, total: 23430 },
    { month: 'Mai', paid: 21560, unpaid: 2780, total: 24340 },
    { month: 'Jun', paid: 24780, unpaid: 3890, total: 28670 },
    { month: 'Jul', paid: 26890, unpaid: 4230, total: 31120 },
    { month: 'Aoû', paid: 23450, unpaid: 3670, total: 27120 },
    { month: 'Sep', paid: 25670, unpaid: 2940, total: 28610 },
    { month: 'Oct', paid: 28340, unpaid: 3450, total: 31790 },
    { month: 'Nov', paid: 24560, unpaid: 4120, total: 28680 },
    { month: 'Déc', paid: 27890, unpaid: 3780, total: 31670 }
  ];

  revenueData = [
    {
      type: 'Frais d\'Adhésion',
      amount: 156780,
      percentage: 52.3,
      color: '#6E56CF',
      icon: 'user'
    },
    {
      type: 'Frais de Réservation',
      amount: 89450,
      percentage: 29.8,
      color: '#B8A9E8',
      icon: 'calendar'
    },
    {
      type: 'Frais Ponctuels',
      amount: 34560,
      percentage: 11.5,
      color: '#34C759',
      icon: 'gift'
    },
    {
      type: 'Cautions',
      amount: 19340,
      percentage: 6.4,
      color: '#FF9500',
      icon: 'safety'
    }
  ];

  // État pour les interactions
  hoveredMonth: string | null = null;
  hoveredRevenue: string | null = null;
  selectedPeriod: 'month' | 'quarter' | 'year' = 'month';

  // Métriques calculées
  get totalRevenue(): number {
    return this.revenueData.reduce((sum, item) => sum + item.amount, 0);
  }

  get totalPaid(): number {
    return this.monthlyData.reduce((sum, item) => sum + item.paid, 0);
  }

  get totalUnpaid(): number {
    return this.monthlyData.reduce((sum, item) => sum + item.unpaid, 0);
  }

  get paymentRate(): number {
    const total = this.totalPaid + this.totalUnpaid;
    return total > 0 ? Math.round((this.totalPaid / total) * 100) : 0;
  }

  get maxAmount(): number {
    return Math.max(...this.monthlyData.map(item => item.total));
  }

  constructor() { }

  ngOnInit(): void {
    // Animation d'entrée
    setTimeout(() => {
      this.animateCards();
    }, 100);
  }

  // Animations
  animateCards(): void {
    const cards = document.querySelectorAll('.stat-card');
    cards.forEach((card, index) => {
      setTimeout(() => {
        card.classList.add('animate-in');
      }, index * 100);
    });
  }

  // Interactions
  onMonthHover(month: string | null): void {
    this.hoveredMonth = month;
  }

  onRevenueHover(type: string | null): void {
    this.hoveredRevenue = type;
  }

  onPeriodChange(period: 'month' | 'quarter' | 'year'): void {
    this.selectedPeriod = period;
    // Ici on pourrait charger de nouvelles données
  }

  // Utilitaires
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  }

  getBarHeight(amount: number): number {
    return (amount / this.maxAmount) * 100;
  }

  getPaidPercentage(month: any): number {
    return (month.paid / month.total) * 100;
  }

  getUnpaidPercentage(month: any): number {
    return (month.unpaid / month.total) * 100;
  }
}
