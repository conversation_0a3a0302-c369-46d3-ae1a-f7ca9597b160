/* DESIGN APPLE-LIKE MODERNE */

.statistics-container {
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* En-tête de page - aligné sur les autres écrans */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px 0;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.period-selector {
  display: flex;
  background: white;
  border: 2px solid #E5E5EA;
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.period-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #8E8E93;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.period-btn.active {
  background: #6E56CF;
  color: white;
  box-shadow: 0 2px 8px rgba(110, 86, 207, 0.3);
}

.period-btn:hover:not(.active) {
  background: rgba(110, 86, 207, 0.1);
  color: #6E56CF;
  transform: translateY(-1px);
}

/* Métriques principales */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6E56CF, #B8A9E8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.metric-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 14px;
  color: #86868b;
  font-weight: 500;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  letter-spacing: -0.5px;
}

.metric-change {
  font-size: 13px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 6px;
  align-self: flex-start;
}

.metric-change.positive {
  color: #34C759;
  background: rgba(52, 199, 89, 0.1);
}

.metric-change.negative {
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
}

/* Progress ring pour le taux de paiement */
.progress-ring {
  position: relative;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-circle {
  transition: stroke-dashoffset 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Graphiques - nouvelle disposition */
.charts-section {
  margin-bottom: 40px;
}

.main-chart {
  margin-bottom: 0px;
}

.revenue-section {
  margin-top: 32px;
  margin-bottom: 32px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 28px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: fit-content;
}

/* Carte principale (Factures Mensuelles) en pleine largeur */
.main-chart .chart-card {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #86868b;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-item.paid .legend-dot {
  background: #34C759;
}

.legend-item.unpaid .legend-dot {
  background: #FF9500;
}

.total-amount {
  font-size: 18px;
  font-weight: 700;
  color: #6E56CF;
}

/* Graphique en barres interactif */
.chart-container {
  height: 320px;
  position: relative;
  flex: 1; /* Prend l'espace disponible */
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 100%;
  gap: 8px;
  padding: 0 8px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 40px;
  position: relative;
  cursor: pointer;
}

.bar-stack {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 260px;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bar-group:hover .bar-stack {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.bar {
  width: 100%;
  transition: all 0.3s ease;
}

.bar.paid {
  background: linear-gradient(180deg, #34C759, #30D158);
}

.bar.unpaid {
  background: linear-gradient(180deg, #FF9500, #FF9F0A);
}

.bar-label {
  margin-top: 12px;
  font-size: 12px;
  color: #86868b;
  font-weight: 500;
}

/* Tooltip interactif */
.bar-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  z-index: 10;
}

.tooltip-content {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 4px;
}

.tooltip-total {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  padding-top: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

/* Graphique des revenus - disposition horizontale */
.revenue-chart {
  height: auto;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.revenue-items {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
}

.revenue-item {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: 20px;
  border-radius: 12px;
  height: auto;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 1px solid #F0F0F0;
  background: #FAFAFA;
}

.revenue-item:first-child {
  margin-top: 0;
}

.revenue-item:not(:first-child) {
  margin-top: 0; /* Supprime le margin supplémentaire */
}

.revenue-item:hover {
  background: rgba(110, 86, 207, 0.05);
  transform: translateX(4px);
}

.revenue-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.revenue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 8px;
}

.revenue-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.revenue-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(110, 86, 207, 0.1);
  color: #6E56CF;
}

.revenue-type {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.3;
}

.revenue-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #8E8E93;
  white-space: nowrap;
}

.revenue-amount {
  font-size: 20px;
  font-weight: 700;
  color: #1C1C1E;
  margin-bottom: 12px;
}

.revenue-bar {
  height: 8px;
  background: #F2F2F7;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 4px;
}

.revenue-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Insights */
.insights-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insight-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  display: flex;
  gap: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.insight-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, #6E56CF, #B8A9E8);
  color: white;
  flex-shrink: 0;
}

.insight-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.insight-content p {
  font-size: 14px;
  color: #86868b;
  margin: 0;
  line-height: 1.4;
}

/* Animations */
.stat-card {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stat-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive */
@media (max-width: 1200px) {
  .revenue-items {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .revenue-item {
    min-height: 120px;
  }
}

/* Responsive pour tablettes */
@media (max-width: 992px) {
  .statistics-container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 28px;
  }

  .header-actions {
    flex-direction: row;
    gap: 12px;
  }

  .period-selector {
    flex-direction: row;
  }

  .period-btn {
    min-width: 70px;
    padding: 6px 12px;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .main-chart {
    margin-bottom: 0px;
  }

  .revenue-section {
    margin-top: 28px;
    margin-bottom: 28px;
  }
}

@media (max-width: 768px) {
  .statistics-container {
    padding: 0 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    text-align: left;
    padding: 16px 0;
  }

  .header-content {
    text-align: left;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
    flex-direction: column;
    gap: 12px;
  }

  .period-selector {
    width: 100%;
    justify-content: stretch;
  }

  .period-btn {
    flex: 1;
    min-width: auto;
    padding: 8px 12px;
    font-size: 14px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }

  .metric-value {
    font-size: 24px;
  }

  .chart-card {
    padding: 16px;
    min-height: auto; /* Supprime la hauteur minimale sur mobile */
  }

  .chart-container {
    height: 280px; /* Hauteur réduite pour mobile */
  }

  .bar-stack {
    height: 220px; /* Hauteur des barres réduite pour mobile */
  }

  .revenue-chart {
    height: auto;
  }

  .revenue-items {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
  }

  .revenue-item {
    padding: 16px;
    min-height: 80px;
  }

  .revenue-icon {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .revenue-type {
    font-size: 14px;
  }

  .revenue-amount {
    font-size: 18px;
  }

  .main-chart {
    margin-bottom: 0px;
  }

  .revenue-section {
    margin-top: 24px;
    margin-bottom: 24px;
  }

  .insights-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .insight-card {
    padding: 16px;
  }

  .insight-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

/* Responsive pour très petits écrans */
@media (max-width: 480px) {
  .statistics-container {
    padding: 0 8px;
  }

  .page-header {
    padding: 12px 0;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .period-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .metrics-grid {
    gap: 8px;
  }

  .metric-card {
    padding: 12px;
  }

  .metric-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .metric-value {
    font-size: 20px;
  }

  .metric-label {
    font-size: 12px;
  }

  .chart-card {
    padding: 12px;
    min-height: auto; /* Supprime la hauteur minimale sur très petits écrans */
  }

  .chart-container {
    height: 250px; /* Hauteur encore plus réduite */
  }

  .bar-stack {
    height: 190px; /* Hauteur des barres très réduite */
  }

  .revenue-chart {
    height: auto;
  }

  .revenue-items {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 12px;
  }

  .revenue-item {
    padding: 14px;
    min-height: 70px;
  }

  .revenue-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }

  .revenue-type {
    font-size: 13px;
  }

  .revenue-amount {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .revenue-percentage {
    font-size: 12px;
  }

  .revenue-bar {
    height: 6px;
  }

  .insight-card {
    padding: 12px;
  }

  .insight-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .insight-content h4 {
    font-size: 14px;
  }

  .main-chart {
    margin-bottom: 0px;
  }

  .revenue-section {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .insight-content p {
    font-size: 12px;
  }
}
