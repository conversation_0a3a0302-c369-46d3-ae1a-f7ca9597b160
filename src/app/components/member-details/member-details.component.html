<div class="member-details-overlay" *ngIf="visible" (click)="close()">
  <div class="member-details-panel" (click)="$event.stopPropagation()">
    <!-- En-tête -->
    <div class="details-header">
      <div class="member-info">
        <div class="member-avatar">
          {{ member?.firstName?.charAt(0) }}{{ member?.lastName?.charAt(0) }}
        </div>
        <div class="member-basic-info">
          <h2 class="member-name">{{ member?.firstName }} {{ member?.lastName }}</h2>
          <p class="member-id">#{{ member?.id }}</p>
          <div class="member-badges">
            <nz-tag [nzColor]="getTypeColor(member?.memberType!)">
              {{ getTypeText(member?.memberType!) }}
            </nz-tag>
            <nz-tag [nzColor]="getStatusColor(member?.status!)">
              {{ getStatusText(member?.status!) }}
            </nz-tag>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <button nz-button nzType="primary" (click)="onEditMember()">
          <nz-icon nzType="edit"></nz-icon>
          Modifier
        </button>
        <button nz-button nzType="text" (click)="close()">
          <nz-icon nzType="close"></nz-icon>
        </button>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="details-content">
      <!-- Informations personnelles -->
      <nz-card nzTitle="Informations personnelles" class="info-card">
        <div class="info-grid">
          <div class="info-item">
            <label>Email</label>
            <span>{{ member?.email }}</span>
          </div>
          <div class="info-item">
            <label>Téléphone</label>
            <span>{{ member?.phone }}</span>
          </div>
          <div class="info-item" *ngIf="member?.studentCode">
            <label>Code étudiant</label>
            <span class="student-code">{{ member?.studentCode }}</span>
          </div>
          <div class="info-item">
            <label>Date d'inscription</label>
            <span>{{ member?.createdAt | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="info-item">
            <label>Dernière modification</label>
            <span>{{ member?.updatedAt | date:'dd/MM/yyyy HH:mm' }}</span>
          </div>
        </div>
      </nz-card>

      <!-- Abonnement -->
      <nz-card nzTitle="Abonnement actuel" class="info-card">
        <div class="subscription-info">
          <div class="subscription-name">
            <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
            <span>{{ member?.subscriptionType }}</span>
          </div>
          <div class="subscription-status">
            <nz-tag [nzColor]="getStatusColor(member?.status!)">
              {{ getStatusText(member?.status!) }}
            </nz-tag>
          </div>
        </div>
      </nz-card>

      <!-- Historique -->
      <nz-card nzTitle="Historique des activités" class="history-card">
        <nz-spin [nzSpinning]="loadingHistory">
          <div *ngIf="history.length > 0; else noHistory">
            <nz-list nzItemLayout="horizontal">
              <nz-list-item *ngFor="let item of history">
                <nz-list-item-meta>
                  <nz-list-item-meta-avatar>
                    <div class="history-icon" [style.background-color]="getHistoryColor(item.type)">
                      <nz-icon [nzType]="getHistoryIcon(item.type)" nzTheme="outline"></nz-icon>
                    </div>
                  </nz-list-item-meta-avatar>
                  <nz-list-item-meta-title>
                    <span>{{ item.description }}</span>
                    <span class="history-amount" *ngIf="item.amount">{{ formatAmount(item.amount) }}</span>
                  </nz-list-item-meta-title>
                  <nz-list-item-meta-description>
                    {{ item.date | date:'dd/MM/yyyy HH:mm' }}
                  </nz-list-item-meta-description>
                </nz-list-item-meta>
              </nz-list-item>
            </nz-list>
          </div>

          <ng-template #noHistory>
            <nz-empty
              nzNotFoundImage="simple"
              nzNotFoundContent="Aucun historique disponible"
            ></nz-empty>
          </ng-template>
        </nz-spin>
      </nz-card>
    </div>
  </div>
</div>
