import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-charts',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './charts.component.html',
  styleUrls: ['./charts.component.css']
})
export class ChartsComponent implements OnInit {

  // Données pour les graphiques temporaires
  monthlyData = [
    { label: 'Jan', paid: 82, unpaid: 18 },
    { label: 'Fév', paid: 87, unpaid: 13 },
    { label: 'Mar', paid: 84, unpaid: 16 },
    { label: 'Avr', paid: 85, unpaid: 15 },
    { label: 'Mai', paid: 89, unpaid: 11 },
    { label: 'Jun', paid: 86, unpaid: 14 },
    { label: 'Jul', paid: 86, unpaid: 14 },
    { label: 'Aoû', paid: 87, unpaid: 13 },
    { label: 'Sep', paid: 90, unpaid: 10 },
    { label: 'Oct', paid: 89, unpaid: 11 },
    { label: 'Nov', paid: 86, unpaid: 14 },
    { label: 'Déc', paid: 88, unpaid: 12 }
  ];

  // Configuration pour le graphique des factures par mois (pour CanvasJS plus tard)
  invoicesChartOptions = {
    animationEnabled: true,
    theme: "light2",
    title: {
      text: "Factures par Mois",
      fontSize: 20,
      fontFamily: "Inter, sans-serif"
    },
    axisX: {
      title: "Mois",
      titleFontSize: 14,
      labelFontSize: 12
    },
    axisY: {
      title: "Montant (€)",
      titleFontSize: 14,
      labelFontSize: 12,
      prefix: "€"
    },
    toolTip: {
      shared: true,
      content: "{name}: €{y}"
    },
    legend: {
      cursor: "pointer",
      itemclick: this.toggleDataSeries.bind(this)
    },
    data: [
      {
        type: "stackedColumn",
        name: "Payées",
        color: "#52c41a", // Vert
        showInLegend: true,
        dataPoints: [
          { label: "Jan", y: 15420 },
          { label: "Fév", y: 18650 },
          { label: "Mar", y: 22340 },
          { label: "Avr", y: 19870 },
          { label: "Mai", y: 21560 },
          { label: "Jun", y: 24780 },
          { label: "Jul", y: 26890 },
          { label: "Aoû", y: 23450 },
          { label: "Sep", y: 25670 },
          { label: "Oct", y: 28340 },
          { label: "Nov", y: 24560 },
          { label: "Déc", y: 27890 }
        ]
      },
      {
        type: "stackedColumn",
        name: "Non Payées",
        color: "#ff4d4f", // Rouge
        showInLegend: true,
        dataPoints: [
          { label: "Jan", y: 3240 },
          { label: "Fév", y: 2890 },
          { label: "Mar", y: 4120 },
          { label: "Avr", y: 3560 },
          { label: "Mai", y: 2780 },
          { label: "Jun", y: 3890 },
          { label: "Jul", y: 4230 },
          { label: "Aoû", y: 3670 },
          { label: "Sep", y: 2940 },
          { label: "Oct", y: 3450 },
          { label: "Nov", y: 4120 },
          { label: "Déc", y: 3780 }
        ]
      }
    ]
  };

  // Configuration pour le graphique des revenus par type
  revenueChartOptions = {
    animationEnabled: true,
    theme: "light2",
    title: {
      text: "Répartition des Revenus par Type",
      fontSize: 20,
      fontFamily: "Inter, sans-serif"
    },
    data: [
      {
        type: "pie",
        startAngle: 240,
        yValueFormatString: "€##,###",
        indexLabel: "{label} {y}",
        indexLabelFontSize: 12,
        toolTipContent: "<b>{label}</b>: €{y} ({percentage}%)",
        dataPoints: [
          {
            y: 156780,
            label: "Membership Fees",
            color: "#6E56CF", // Violet principal
            percentage: 52.3
          },
          {
            y: 89450,
            label: "Booking Fees",
            color: "#B8A9E8", // Violet soft
            percentage: 29.8
          },
          {
            y: 34560,
            label: "One-off Fees",
            color: "#52c41a", // Vert
            percentage: 11.5
          },
          {
            y: 19340,
            label: "Deposits",
            color: "#1890ff", // Bleu
            percentage: 6.4
          }
        ]
      }
    ]
  };

  constructor() { }

  ngOnInit(): void {
    // Ici on pourrait charger les données depuis un service
    this.loadChartsData();
  }

  // Fonction pour basculer l'affichage des séries de données
  toggleDataSeries(e: any) {
    if (typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
      e.dataSeries.visible = false;
    } else {
      e.dataSeries.visible = true;
    }
    e.chart.render();
  }

  // Charger les données des graphiques (à remplacer par un service réel)
  private loadChartsData(): void {
    // TODO: Remplacer par des appels API réels
    console.log('Chargement des données des graphiques...');
  }
}
