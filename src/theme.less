
// Custom Theming for NG-ZORRO - Workeem Brand Colors
// For more information: https://ng.ant.design/docs/customize-theme/en
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";

// Workeem Brand Colors
@primary-color: #6E56CF; // Deep Indigo - Logo, boutons principaux, texte important
@primary-color-hover: #8B73D6; // Slightly lighter for hover states
@primary-color-active: #5A47B8; // Slightly darker for active states

// Soft primary color for backgrounds and subtle elements
@primary-1: #EDE9F8; // Lavender Mist - Arrière-plans clairs, hover, sections douces
@primary-2: #DDD4F3;
@primary-3: #CCBFEE;
@primary-4: #BBAAE9;
@primary-5: #AA95E4;
@primary-6: #9980DF;
@primary-7: #886BDA;

// Apple-like neutral colors
@text-color: #1C1C1E; // Gris foncé <PERSON>-like
@text-color-secondary: #3A3A3C;
@text-color-inverse: #FFFFFF;
@background-color-light: #F2F2F7; // Gris clair Apple-like
@background-color-base: #FFFFFF;

// Layout colors
@layout-body-background: @background-color-light;
@layout-header-background: @background-color-base;
@layout-sider-background: @background-color-base;

// Menu colors
@menu-dark-bg: @primary-color;
@menu-dark-submenu-bg: darken(@primary-color, 5%);
@menu-dark-item-active-bg: @primary-color-active;
@menu-dark-selected-item-icon-color: @background-color-light;
@menu-dark-selected-item-text-color: @background-color-light;

// Border and shadow
@border-color-base: #E5E5EA;
@border-radius-base: 8px;
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.06);

// Component specific overrides
@btn-border-radius-base: @border-radius-base;
@card-radius: @border-radius-base;
@input-border-color: @border-color-base;
@input-hover-border-color: @primary-color;
