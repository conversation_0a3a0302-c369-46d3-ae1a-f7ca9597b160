package com.workeem.workeem_api.business.site.web.dto;

import com.workeem.workeem_api.business.site.domain.Site;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SiteRequestDto {

    @NotBlank(message = "Site name is required")
    @Size(max = 100, message = "Site name must not exceed 100 characters")
    private String name;

    @NotBlank(message = "City is required")
    @Size(max = 50, message = "City must not exceed 50 characters")
    private String city;

    @NotBlank(message = "Country is required")
    @Size(max = 50, message = "Country must not exceed 50 characters")
    private String country;

    @NotBlank(message = "Address is required")
    @Size(max = 255, message = "Address must not exceed 255 characters")
    private String address;

    @Size(max = 255, message = "Image URL must not exceed 255 characters")
    private String image;

    private Boolean isActive;
    private Integer spacesCount;
    private Integer membersCount;

    /**
     * Convert SiteRequestDto to Site entity
     */
    public Site toEntity() {
        return Site.builder()
                .name(this.name)
                .city(this.city)
                .country(this.country)
                .address(this.address)
                .image(this.image)
                .isActive(this.isActive != null ? this.isActive : true)
                .spacesCount(this.spacesCount != null ? this.spacesCount : 0)
                .membersCount(this.membersCount != null ? this.membersCount : 0)
                .build();
    }
}
