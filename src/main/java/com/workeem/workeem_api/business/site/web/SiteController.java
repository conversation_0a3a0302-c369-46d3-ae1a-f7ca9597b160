package com.workeem.workeem_api.business.site.web;

import com.workeem.workeem_api.business.site.application.SiteService;
import com.workeem.workeem_api.business.site.domain.Site;
import com.workeem.workeem_api.business.site.web.dto.SiteRequestDto;
import com.workeem.workeem_api.business.site.web.dto.SiteResponseDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/sites")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class SiteController implements Serializable {

    private final SiteService siteService;

    /**
     * Get all sites
     */
    @GetMapping
    public ResponseEntity<List<SiteResponseDto>> getAllSites(@RequestParam(required = false) Boolean active) {
        log.debug("GET /api/sites - active: {}", active);

        List<Site> sites;
        if (active != null && active) {
            sites = siteService.getActiveSites();
        } else {
            sites = siteService.getAllSites();
        }

        List<SiteResponseDto> siteDtos = sites.stream()
                .map(SiteResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(siteDtos);
    }

    /**
     * Get site by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<SiteResponseDto> getSiteById(@PathVariable Long id) {
        log.debug("GET /api/sites/{}", id);

        Site site = siteService.getSiteById(id);
        SiteResponseDto siteDto = SiteResponseDto.fromEntity(site);
        return ResponseEntity.ok(siteDto);
    }

    /**
     * Create a new site
     */
    @PostMapping
    public ResponseEntity<SiteResponseDto> createSite(@Valid @RequestBody SiteRequestDto siteRequestDto) {
        log.debug("POST /api/sites - Creating site: {}", siteRequestDto.getName());

        Site site = siteRequestDto.toEntity();
        Site createdSite = siteService.createSite(site);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(createdSite);

        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing site
     */
    @PutMapping("/{id}")
    public ResponseEntity<SiteResponseDto> updateSite(@PathVariable Long id, @Valid @RequestBody SiteRequestDto siteRequestDto) {
        log.debug("PUT /api/sites/{} - Updating site", id);

        Site site = siteRequestDto.toEntity();
        Site updatedSite = siteService.updateSite(id, site);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(updatedSite);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a site
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSite(@PathVariable Long id) {
        log.debug("DELETE /api/sites/{}", id);
        
        siteService.deleteSiteById(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Toggle site status (activate/deactivate)
     */
    @PatchMapping("/{id}/toggle-status")
    public ResponseEntity<SiteResponseDto> toggleSiteStatus(@PathVariable Long id) {
        log.debug("PATCH /api/sites/{}/toggle-status", id);

        Site updatedSite = siteService.toggleSiteStatus(id);
        SiteResponseDto responseDto = SiteResponseDto.fromEntity(updatedSite);
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Get sites by city
     */
    @GetMapping("/by-city/{city}")
    public ResponseEntity<List<SiteResponseDto>> getSitesByCity(@PathVariable String city) {
        log.debug("GET /api/sites/by-city/{}", city);

        List<Site> sites = siteService.getSitesByCity(city);
        List<SiteResponseDto> siteDtos = sites.stream()
                .map(SiteResponseDto::fromEntity)
                .collect(Collectors.toList());
        return ResponseEntity.ok(siteDtos);
    }

    /**
     * Get sites by country
     */
    @GetMapping("/by-country/{country}")
    public ResponseEntity<List<SiteResponseDto>> getSitesByCountry(@PathVariable String country) {
        log.debug("GET /api/sites/by-country/{}", country);

        List<Site> sites = siteService.getSitesByCountry(country);
        List<SiteResponseDto> siteDtos = sites.stream()
                .map(SiteResponseDto::fromEntity)
                .collect(Collectors.toList());
        return ResponseEntity.ok(siteDtos);
    }

    /**
     * Update spaces count for a site
     */
    @PatchMapping("/{id}/spaces-count")
    public ResponseEntity<Void> updateSpacesCount(@PathVariable Long id, @RequestParam Integer count) {
        log.debug("PATCH /api/sites/{}/spaces-count - count: {}", id, count);
        
        siteService.updateSpacesCount(id, count);
        return ResponseEntity.ok().build();
    }

    /**
     * Update members count for a site
     */
    @PatchMapping("/{id}/members-count")
    public ResponseEntity<Void> updateMembersCount(@PathVariable Long id, @RequestParam Integer count) {
        log.debug("PATCH /api/sites/{}/members-count - count: {}", id, count);
        
        siteService.updateMembersCount(id, count);
        return ResponseEntity.ok().build();
    }
}
