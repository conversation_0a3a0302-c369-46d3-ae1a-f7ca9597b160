package com.workeem.workeem_api.business.site.domain;

public enum SpaceStatus {
    AVAILABLE("available"),
    OCCUPIED("occupied"),
    MAINTENANCE("maintenance"),
    OUT_OF_ORDER("out_of_order"),
    RESERVED("reserved");

    private final String value;

    SpaceStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static SpaceStatus fromValue(String value) {
        for (SpaceStatus status : SpaceStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown SpaceStatus value: " + value);
    }
}
