package com.workeem.workeem_api.business.site.infrastructure;

import com.workeem.workeem_api.business.site.domain.Site;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SiteRepository extends JpaRepository<Site, Long> {

    /**
     * Find all active sites
     */
    List<Site> findByIsActiveTrue();

    /**
     * Find sites by city
     */
    List<Site> findByCityIgnoreCase(String city);

    /**
     * Find sites by country
     */
    List<Site> findByCountryIgnoreCase(String country);

    /**
     * Find site by name (case insensitive)
     */
    Optional<Site> findByNameIgnoreCase(String name);

    /**
     * Check if site name exists (for validation)
     */
    boolean existsByNameIgnoreCase(String name);

    /**
     * Get total spaces count across all sites
     */
    @Query("SELECT COALESCE(SUM(s.spacesCount), 0) FROM Site s WHERE s.isActive = true")
    Long getTotalSpacesCount();

    /**
     * Get total members count across all sites
     */
    @Query("SELECT COALESCE(SUM(s.membersCount), 0) FROM Site s WHERE s.isActive = true")
    Long getTotalMembersCount();
}
