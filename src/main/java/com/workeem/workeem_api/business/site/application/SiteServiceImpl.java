package com.workeem.workeem_api.business.site.application;

import com.workeem.workeem_api.business.site.domain.Site;
import com.workeem.workeem_api.business.site.infrastructure.SiteRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SiteServiceImpl implements SiteService {

    private final SiteRepository siteRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Site> getAllSites() {
        log.debug("Fetching all sites");
        return siteRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Site> getActiveSites() {
        log.debug("Fetching all active sites");
        return siteRepository.findByIsActiveTrue();
    }

    @Override
    @Transactional(readOnly = true)
    public Site getSiteById(Long id) {
        log.debug("Fetching site with ID: {}", id);
        return siteRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Site with ID " + id + " not found"));
    }

    @Override
    public Site createSite(Site site) {
        log.debug("Creating new site: {}", site.getName());
        
        // Check if site name already exists
        if (siteRepository.existsByNameIgnoreCase(site.getName())) {
            throw new DataIntegrityViolationException("Site with name '" + site.getName() + "' already exists");
        }

        // Set default values
        if (site.getIsActive() == null) {
            site.setIsActive(true);
        }
        if (site.getSpacesCount() == null) {
            site.setSpacesCount(0);
        }
        if (site.getMembersCount() == null) {
            site.setMembersCount(0);
        }

        Site savedSite = siteRepository.save(site);
        log.info("Site created successfully with ID: {}", savedSite.getSiteId());
        return savedSite;
    }

    @Override
    public Site updateSite(Long siteId, Site site) {
        log.debug("Updating site with ID: {}", siteId);
        
        Site existingSite = getSiteById(siteId);

        // Check if new name conflicts with existing sites (excluding current site)
        if (!existingSite.getName().equalsIgnoreCase(site.getName()) && 
            siteRepository.existsByNameIgnoreCase(site.getName())) {
            throw new DataIntegrityViolationException("Site with name '" + site.getName() + "' already exists");
        }

        // Update fields
        existingSite.setName(site.getName());
        existingSite.setCity(site.getCity());
        existingSite.setCountry(site.getCountry());
        existingSite.setAddress(site.getAddress());
        existingSite.setImage(site.getImage());
        
        if (site.getIsActive() != null) {
            existingSite.setIsActive(site.getIsActive());
        }
        if (site.getSpacesCount() != null) {
            existingSite.setSpacesCount(site.getSpacesCount());
        }
        if (site.getMembersCount() != null) {
            existingSite.setMembersCount(site.getMembersCount());
        }

        Site updatedSite = siteRepository.save(existingSite);
        log.info("Site updated successfully with ID: {}", updatedSite.getSiteId());
        return updatedSite;
    }

    @Override
    public void deleteSiteById(Long id) {
        log.debug("Deleting site with ID: {}", id);
        
        if (!siteRepository.existsById(id)) {
            throw new EntityNotFoundException("Site with ID " + id + " not found");
        }

        siteRepository.deleteById(id);
        log.info("Site deleted successfully with ID: {}", id);
    }

    @Override
    public Site toggleSiteStatus(Long siteId) {
        log.debug("Toggling status for site with ID: {}", siteId);
        
        Site site = getSiteById(siteId);
        site.setIsActive(!site.getIsActive());
        
        Site updatedSite = siteRepository.save(site);
        log.info("Site status toggled successfully for ID: {}, new status: {}", siteId, updatedSite.getIsActive());
        return updatedSite;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Site> getSitesByCity(String city) {
        log.debug("Fetching sites by city: {}", city);
        return siteRepository.findByCityIgnoreCase(city);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Site> getSitesByCountry(String country) {
        log.debug("Fetching sites by country: {}", country);
        return siteRepository.findByCountryIgnoreCase(country);
    }

    @Override
    public void updateSpacesCount(Long siteId, Integer spacesCount) {
        log.debug("Updating spaces count for site ID: {} to {}", siteId, spacesCount);
        
        Site site = getSiteById(siteId);
        site.setSpacesCount(spacesCount);
        siteRepository.save(site);
        
        log.info("Spaces count updated successfully for site ID: {}", siteId);
    }

    @Override
    public void updateMembersCount(Long siteId, Integer membersCount) {
        log.debug("Updating members count for site ID: {} to {}", siteId, membersCount);
        
        Site site = getSiteById(siteId);
        site.setMembersCount(membersCount);
        siteRepository.save(site);
        
        log.info("Members count updated successfully for site ID: {}", siteId);
    }
}
