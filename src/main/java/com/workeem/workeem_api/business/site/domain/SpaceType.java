package com.workeem.workeem_api.business.site.domain;

public enum SpaceType {
    WORKSTATION("workstation"),
    PRIVATE_OFFICE("private_office"),
    MEETING_ROOM("meeting_room"),
    PHONE_BOOTH("phone_booth"),
    LOUNGE("lounge"),
    CONFERENCE_ROOM("conference_room"),
    HOT_DESK("hot_desk"),
    DEDICATED_DESK("dedicated_desk"),
    COLLABORATIVE("collaborative");

    private final String value;

    SpaceType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static SpaceType fromValue(String value) {
        for (SpaceType type : SpaceType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown SpaceType value: " + value);
    }
}
