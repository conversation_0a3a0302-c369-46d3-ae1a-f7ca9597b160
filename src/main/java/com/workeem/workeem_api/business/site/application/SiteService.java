package com.workeem.workeem_api.business.site.application;

import com.workeem.workeem_api.business.site.domain.Site;

import java.util.List;

public interface SiteService {

    /**
     * Get all sites
     */
    List<Site> getAllSites();

    /**
     * Get all active sites
     */
    List<Site> getActiveSites();

    /**
     * Get site by ID
     */
    Site getSiteById(Long id);

    /**
     * Create a new site
     */
    Site createSite(Site site);

    /**
     * Update an existing site
     */
    Site updateSite(Long siteId, Site site);

    /**
     * Delete a site by ID
     */
    void deleteSiteById(Long id);

    /**
     * Activate/Deactivate a site
     */
    Site toggleSiteStatus(Long siteId);

    /**
     * Get sites by city
     */
    List<Site> getSitesByCity(String city);

    /**
     * Get sites by country
     */
    List<Site> getSitesByCountry(String country);

    /**
     * Update spaces count for a site
     */
    void updateSpacesCount(Long siteId, Integer spacesCount);

    /**
     * Update members count for a site
     */
    void updateMembersCount(Long siteId, Integer membersCount);
}
