import { Component, OnInit, signal } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzEmptyModule } from 'ng-zorro-antd/empty';

import {
  Space,
  SpaceType,
  SpaceStatus,
  Equipment,
  EquipmentType,
  EquipmentStatus,
  CreateSpaceRequest,
  UpdateSpaceRequest
} from '../../models/space.model';
import { SpaceService } from '../../services/space.service';

@Component({
  selector: 'app-space-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzFormModule,
    NzInputNumberModule,
    NzSwitchModule,
    NzTimePickerModule,
    NzTagModule,
    NzDividerModule,
    NzSpinModule,
    NzGridModule,
    NzCheckboxModule,
    NzEmptyModule
  ],
  templateUrl: './space-form.component.html',
  styleUrl: './space-form.component.css'
})
export class SpaceFormComponent implements OnInit {
  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private savingSignal = signal<boolean>(false);
  private spaceSignal = signal<Space | null>(null);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get saving() { return this.savingSignal(); }
  get space() { return this.spaceSignal(); }

  // Formulaire
  spaceForm!: FormGroup;
  isEditMode = false;
  spaceId: string | null = null;

  // Enums pour les templates
  SpaceType = SpaceType;
  SpaceStatus = SpaceStatus;
  EquipmentType = EquipmentType;
  EquipmentStatus = EquipmentStatus;

  // Options pour les selects
  spaceTypeOptions = [
    { label: 'Poste de travail', value: SpaceType.WORKSTATION },
    { label: 'Bureau privé', value: SpaceType.PRIVATE_OFFICE },
    { label: 'Salle de réunion', value: SpaceType.MEETING_ROOM },
    { label: 'Cabine téléphonique', value: SpaceType.PHONE_BOOTH },
    { label: 'Espace détente', value: SpaceType.LOUNGE },
    { label: 'Salle de conférence', value: SpaceType.CONFERENCE_ROOM },
    { label: 'Bureau partagé', value: SpaceType.HOT_DESK },
    { label: 'Bureau dédié', value: SpaceType.DEDICATED_DESK }
  ];

  equipmentTypeOptions = [
    { label: 'Ordinateur', value: EquipmentType.COMPUTER },
    { label: 'Écran', value: EquipmentType.MONITOR },
    { label: 'Imprimante', value: EquipmentType.PRINTER },
    { label: 'Projecteur', value: EquipmentType.PROJECTOR },
    { label: 'Tableau blanc', value: EquipmentType.WHITEBOARD },
    { label: 'Écran TV', value: EquipmentType.TV_SCREEN },
    { label: 'Téléphone', value: EquipmentType.PHONE },
    { label: 'Webcam', value: EquipmentType.WEBCAM },
    { label: 'Microphone', value: EquipmentType.MICROPHONE },
    { label: 'Haut-parleurs', value: EquipmentType.SPEAKERS },
    { label: 'Bureau', value: EquipmentType.DESK },
    { label: 'Chaise', value: EquipmentType.CHAIR },
    { label: 'Rangement', value: EquipmentType.STORAGE },
    { label: 'WiFi', value: EquipmentType.WIFI },
    { label: 'Ethernet', value: EquipmentType.ETHERNET }
  ];

  // Commodités prédéfinies
  availableAmenities = [
    'WiFi', 'Climatisation', 'Chauffage', 'Fenêtre', 'Lumière naturelle',
    'Prise électrique', 'Éclairage LED', 'Isolation phonique', 'Accès handicapé',
    'Parking', 'Cuisine', 'Machine à café', 'Réfrigérateur', 'Micro-ondes'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private spaceService: SpaceService,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    this.spaceId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.spaceId;

    if (this.isEditMode && this.spaceId) {
      this.loadSpace(this.spaceId);
    }
  }

  private initializeForm() {
    this.spaceForm = this.fb.group({
      // Informations de base - seul le nom est requis
      name: ['', [Validators.required, Validators.minLength(2)]],
      description: [''],
      type: [null],
      capacity: [1, [Validators.min(1)]],
      location: [''],
      floor: [''],
      area: [1, [Validators.min(1)]],

      // Équipements
      equipment: this.fb.array([]),

      // Commodités
      amenities: [[]],

      // Règles
      rules: this.fb.array([]),

      // Disponibilités
      availability: this.fb.group({
        isActive: [true],
        advanceBookingDays: [30, [Validators.min(1)]],
        minBookingDuration: [60, [Validators.min(15)]],
        maxBookingDuration: [480, [Validators.min(60)]],
        bufferTime: [15, [Validators.min(0)]],
        schedule: this.fb.group({
          monday: this.createDayScheduleGroup(),
          tuesday: this.createDayScheduleGroup(),
          wednesday: this.createDayScheduleGroup(),
          thursday: this.createDayScheduleGroup(),
          friday: this.createDayScheduleGroup(),
          saturday: this.createDayScheduleGroup(),
          sunday: this.createDayScheduleGroup()
        })
      }),

      // Tarification
      pricing: this.fb.group({
        hourlyRate: [0, [Validators.min(0)]],
        dailyRate: [0, [Validators.min(0)]],
        weeklyRate: [0, [Validators.min(0)]],
        monthlyRate: [0, [Validators.min(0)]],
        currency: ['EUR']
      })
    });
  }

  private createDayScheduleGroup() {
    return this.fb.group({
      isOpen: [true],
      openTime: ['08:00'],
      closeTime: ['18:00'],
      breaks: this.fb.array([])
    });
  }

  private loadSpace(id: string) {
    this.loadingSignal.set(true);
    this.spaceService.getSpaceById(id).subscribe({
      next: (space) => {
        if (space) {
          this.spaceSignal.set(space);
          this.populateForm(space);
        } else {
          this.message.error('Espace non trouvé');
          this.goBack();
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement:', error);
        this.message.error('Erreur lors du chargement de l\'espace');
        this.loadingSignal.set(false);
        this.goBack();
      }
    });
  }

  private populateForm(space: Space) {
    // Remplir les équipements
    const equipmentArray = this.spaceForm.get('equipment') as FormArray;
    equipmentArray.clear();
    space.equipment.forEach(equipment => {
      equipmentArray.push(this.createEquipmentGroup(equipment));
    });

    // Remplir les règles
    const rulesArray = this.spaceForm.get('rules') as FormArray;
    rulesArray.clear();
    space.rules.forEach(rule => {
      rulesArray.push(this.fb.control(rule, [Validators.required]));
    });

    // Remplir le formulaire
    this.spaceForm.patchValue({
      name: space.name,
      description: space.description,
      type: space.type,
      capacity: space.capacity,
      location: space.location,
      floor: space.floor,
      area: space.area,
      amenities: space.amenities,
      availability: {
        isActive: space.availability.isActive,
        advanceBookingDays: space.availability.advanceBookingDays,
        minBookingDuration: space.availability.minBookingDuration,
        maxBookingDuration: space.availability.maxBookingDuration,
        bufferTime: space.availability.bufferTime,
        schedule: {
          monday: {
            isOpen: space.availability.schedule.monday.isOpen,
            openTime: space.availability.schedule.monday.openTime,
            closeTime: space.availability.schedule.monday.closeTime
          },
          tuesday: {
            isOpen: space.availability.schedule.tuesday.isOpen,
            openTime: space.availability.schedule.tuesday.openTime,
            closeTime: space.availability.schedule.tuesday.closeTime
          },
          wednesday: {
            isOpen: space.availability.schedule.wednesday.isOpen,
            openTime: space.availability.schedule.wednesday.openTime,
            closeTime: space.availability.schedule.wednesday.closeTime
          },
          thursday: {
            isOpen: space.availability.schedule.thursday.isOpen,
            openTime: space.availability.schedule.thursday.openTime,
            closeTime: space.availability.schedule.thursday.closeTime
          },
          friday: {
            isOpen: space.availability.schedule.friday.isOpen,
            openTime: space.availability.schedule.friday.openTime,
            closeTime: space.availability.schedule.friday.closeTime
          },
          saturday: {
            isOpen: space.availability.schedule.saturday.isOpen,
            openTime: space.availability.schedule.saturday.openTime,
            closeTime: space.availability.schedule.saturday.closeTime
          },
          sunday: {
            isOpen: space.availability.schedule.sunday.isOpen,
            openTime: space.availability.schedule.sunday.openTime,
            closeTime: space.availability.schedule.sunday.closeTime
          }
        }
      },
      pricing: {
        hourlyRate: space.pricing.hourlyRate,
        dailyRate: space.pricing.dailyRate,
        weeklyRate: space.pricing.weeklyRate,
        monthlyRate: space.pricing.monthlyRate,
        currency: space.pricing.currency
      }
    });
  }

  // Getters pour les FormArrays
  get equipmentArray() {
    return this.spaceForm.get('equipment') as FormArray;
  }

  get rulesArray() {
    return this.spaceForm.get('rules') as FormArray;
  }

  // Gestion des équipements
  createEquipmentGroup(equipment?: Equipment) {
    return this.fb.group({
      id: [equipment?.id || Date.now().toString()],
      name: [equipment?.name || '', [Validators.required]],
      type: [equipment?.type || null, [Validators.required]],
      brand: [equipment?.brand || ''],
      model: [equipment?.model || ''],
      quantity: [equipment?.quantity || 1, [Validators.required, Validators.min(1)]],
      status: [equipment?.status || EquipmentStatus.WORKING, [Validators.required]],
      description: [equipment?.description || '']
    });
  }

  addEquipment() {
    this.equipmentArray.push(this.createEquipmentGroup());
  }

  removeEquipment(index: number) {
    this.equipmentArray.removeAt(index);
  }

  // Gestion des règles
  addRule() {
    this.rulesArray.push(this.fb.control('', [Validators.required]));
  }

  removeRule(index: number) {
    this.rulesArray.removeAt(index);
  }

  // Navigation
  goBack() {
    this.location.back();
  }

  // Soumission du formulaire
  onSubmit() {
    if (this.spaceForm.valid) {
      this.savingSignal.set(true);
      const formValue = this.spaceForm.value;

      if (this.isEditMode && this.spaceId) {
        this.updateSpace(formValue);
      } else {
        this.createSpace(formValue);
      }
    } else {
      this.markFormGroupTouched(this.spaceForm);
      this.message.error('Veuillez corriger les erreurs dans le formulaire');
    }
  }

  private createSpace(formValue: any) {
    const request: CreateSpaceRequest = {
      name: formValue.name,
      description: formValue.description,
      type: formValue.type,
      capacity: formValue.capacity,
      location: formValue.location,
      floor: formValue.floor,
      area: formValue.area,
      equipment: formValue.equipment,
      amenities: formValue.amenities,
      availability: {
        ...formValue.availability,
        exceptions: []
      },
      pricing: {
        ...formValue.pricing,
        discounts: []
      },
      rules: formValue.rules
    };

    this.spaceService.createSpace(request).subscribe({
      next: (space) => {
        this.message.success('Espace créé avec succès');
        this.router.navigate(['/spaces/details', space.id]);
        this.savingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);
        this.message.error('Erreur lors de la création de l\'espace');
        this.savingSignal.set(false);
      }
    });
  }

  private updateSpace(formValue: any) {
    if (!this.spaceId) return;

    const request: UpdateSpaceRequest = {
      id: this.spaceId,
      name: formValue.name,
      description: formValue.description,
      type: formValue.type,
      capacity: formValue.capacity,
      location: formValue.location,
      floor: formValue.floor,
      area: formValue.area,
      equipment: formValue.equipment,
      amenities: formValue.amenities,
      availability: {
        ...formValue.availability,
        exceptions: this.space?.availability.exceptions || []
      },
      pricing: {
        ...formValue.pricing,
        discounts: this.space?.pricing.discounts || []
      },
      rules: formValue.rules
    };

    this.spaceService.updateSpace(request).subscribe({
      next: (space) => {
        this.message.success('Espace modifié avec succès');
        this.router.navigate(['/spaces/details', space.id]);
        this.savingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors de la modification:', error);
        this.message.error('Erreur lors de la modification de l\'espace');
        this.savingSignal.set(false);
      }
    });
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }

  // Méthodes utilitaires pour les templates
  getEquipmentTypeLabel(type: EquipmentType): string {
    const option = this.equipmentTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
  }

  getDayLabel(day: string): string {
    const dayLabels: { [key: string]: string } = {
      'monday': 'Lundi',
      'tuesday': 'Mardi',
      'wednesday': 'Mercredi',
      'thursday': 'Jeudi',
      'friday': 'Vendredi',
      'saturday': 'Samedi',
      'sunday': 'Dimanche'
    };
    return dayLabels[day] || day;
  }
}
