<div class="billing-form-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="file-text" nzTheme="outline"></nz-icon>
        {{ isEditMode ? 'Modifier la facture' : 'Nouvelle facture' }}
      </h1>
      <p class="page-description">
        {{ isEditMode ? 'Modifiez les informations de la facture' : 'Créez une nouvelle facture pour un membre' }}
      </p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="text" nzSize="large" (click)="onCancel()" class="back-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
    </div>
  </div>

  <form nz-form [formGroup]="billingForm" (ngSubmit)="onSubmit()" [nzLayout]="'vertical'">
    <div class="form-grid">
      <!-- Informations générales -->
      <nz-card class="form-card" nzTitle="Informations générales">
        <div class="form-row">
          <nz-form-item class="form-item">
            <nz-form-label nzRequired>Client</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner un client">
              <nz-select formControlName="memberId" nzPlaceholder="Sélectionner un client" nzShowSearch>
                <nz-option *ngFor="let member of members" [nzValue]="member.id" [nzLabel]="member.firstName + ' ' + member.lastName">
                  <div class="member-option">
                    <div class="member-name">{{ member.firstName }} {{ member.lastName }}</div>
                    <div class="member-email">{{ member.email }}</div>
                  </div>
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div class="form-row">
          <nz-form-item class="form-item">
            <nz-form-label nzRequired>Date d'émission</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner une date d'émission">
              <nz-date-picker formControlName="issueDate" nzFormat="dd/MM/yyyy" style="width: 100%"></nz-date-picker>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="form-item">
            <nz-form-label nzRequired>Date d'échéance</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner une date d'échéance">
              <nz-date-picker formControlName="dueDate" nzFormat="dd/MM/yyyy" style="width: 100%"></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div class="form-row">
          <nz-form-item class="form-item full-width">
            <nz-form-label>Notes</nz-form-label>
            <nz-form-control>
              <textarea nz-input formControlName="notes" placeholder="Notes additionnelles (optionnel)" rows="3"></textarea>
            </nz-form-control>
          </nz-form-item>
        </div>
      </nz-card>

      <!-- Articles/Services -->
      <nz-card class="form-card" nzTitle="Articles et services">
        <div class="items-section">
          <div formArrayName="items">
            <div *ngFor="let item of items.controls; let i = index" [formGroupName]="i" class="item-row">
              <div class="item-form">
                <nz-form-item class="item-description">
                  <nz-form-label nzRequired>Description</nz-form-label>
                  <nz-form-control nzErrorTip="Description requise">
                    <input nz-input formControlName="description" placeholder="Description de l'article/service" />
                  </nz-form-control>
                </nz-form-item>

                <nz-form-item class="item-type">
                  <nz-form-label nzRequired>Type</nz-form-label>
                  <nz-form-control>
                    <nz-select formControlName="type">
                      <nz-option nzValue="subscription" nzLabel="Abonnement"></nz-option>
                      <nz-option nzValue="reservation" nzLabel="Réservation"></nz-option>
                      <nz-option nzValue="service" nzLabel="Service"></nz-option>
                      <nz-option nzValue="product" nzLabel="Produit"></nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>

                <nz-form-item class="item-quantity">
                  <nz-form-label nzRequired>Quantité</nz-form-label>
                  <nz-form-control nzErrorTip="Quantité requise">
                    <nz-input-number formControlName="quantity" [nzMin]="1" [nzStep]="1" style="width: 100%"></nz-input-number>
                  </nz-form-control>
                </nz-form-item>

                <nz-form-item class="item-price">
                  <nz-form-label nzRequired>Prix unitaire (MAD)</nz-form-label>
                  <nz-form-control nzErrorTip="Prix requis">
                    <nz-input-number formControlName="unitPrice" [nzMin]="0" [nzStep]="0.01" [nzPrecision]="2" style="width: 100%"></nz-input-number>
                  </nz-form-control>
                </nz-form-item>

                <div class="item-total">
                  <label>Total</label>
                  <div class="total-amount">{{ getItemTotal(i) | currency:'MAD ':'symbol':'1.2-2' }}</div>
                </div>

                <div class="item-actions">
                  <button nz-button nzType="text" nzDanger (click)="removeItem(i)" [disabled]="items.length === 1">
                    <nz-icon nzType="delete"></nz-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="add-item-section">
            <button nz-button nzType="dashed" (click)="addItem()" class="add-item-btn">
              <nz-icon nzType="plus"></nz-icon>
              Ajouter un article
            </button>
          </div>
        </div>

        <nz-divider></nz-divider>

        <!-- Totaux -->
        <div class="totals-section">
          <div class="totals-row">
            <span>Sous-total:</span>
            <span>{{ getSubtotal() | currency:'MAD ':'symbol':'1.2-2' }}</span>
          </div>
          <div class="totals-row">
            <span>TVA (20%):</span>
            <span>{{ getTaxAmount() | currency:'MAD ':'symbol':'1.2-2' }}</span>
          </div>
          <div class="totals-row total-final">
            <span><strong>Total:</strong></span>
            <span><strong>{{ getTotal() | currency:'MAD ':'symbol':'1.2-2' }}</strong></span>
          </div>
        </div>
      </nz-card>
    </div>

    <!-- Actions -->
    <div class="form-actions">
      <button nz-button nzType="default" nzSize="large" (click)="onCancel()">
        Annuler
      </button>
      <button nz-button nzType="primary" nzSize="large" [nzLoading]="submitting" nzHtmlType="submit">
        {{ isEditMode ? 'Mettre à jour' : 'Créer la facture' }}
      </button>
    </div>
  </form>
</div>
