<div class="reservations-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        Planning des réservations
      </h1>
      <p class="page-description"><PERSON><PERSON><PERSON> les réservations d'espaces et visualisez la disponibilité</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="primary" nzSize="large" (click)="navigateToReservationForm()" class="add-button">
        <nz-icon nzType="plus"></nz-icon>
        Nouvelle réservation
      </button>
    </div>
  </div>

  <!-- Filtres rapides -->
  <nz-card class="filters-card" nzTitle="Filtres">
    <div class="filters-row">
      <div class="filter-item">
        <label>Espace</label>
        <nz-select
          [(ngModel)]="selectedSpaceId"
          nzPlaceHolder="Tous les espaces"
          nzAllowClear
          (ngModelChange)="onSpaceFilterChange($event)"
        >
          <nz-option
            *ngFor="let space of spaces"
            [nzLabel]="space.name"
            [nzValue]="space.id"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Statut</label>
        <nz-select
          [(ngModel)]="selectedStatus"
          nzPlaceHolder="Tous les statuts"
          nzAllowClear
          (ngModelChange)="onStatusFilterChange($event)"
        >
          <nz-option nzLabel="Confirmée" nzValue="confirmed"></nz-option>
          <nz-option nzLabel="En attente" nzValue="pending"></nz-option>
          <nz-option nzLabel="Annulée" nzValue="cancelled"></nz-option>
        </nz-select>
      </div>
    </div>
  </nz-card>

  <!-- FullCalendar -->
  <div class="calendar-section">
    <div class="calendar-legend">
      <div class="legend-title">Légende :</div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color confirmed"></div>
          <span>Confirmé</span>
        </div>
        <div class="legend-item">
          <div class="legend-color pending"></div>
          <span>En attente</span>
        </div>
        <div class="legend-item">
          <div class="legend-color cancelled"></div>
          <span>Annulé</span>
        </div>
      </div>
    </div>

    <full-calendar
      [options]="calendarOptions"
      #calendar
    ></full-calendar>
  </div>
</div>
