import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-unauthorized',
  standalone: true,
  imports: [
    CommonModule,
    NzButtonModule,
    NzIconModule
  ],
  template: `
    <div class="unauthorized-container">
      <div class="unauthorized-content">
        <!-- Icône d'erreur -->
        <div class="error-icon">
          <nz-icon nzType="exclamation-circle" nzTheme="outline"></nz-icon>
        </div>

        <!-- Titre et message -->
        <div class="error-message">
          <h1>Accès non autorisé</h1>
          <p>Vous n'avez pas les permissions nécessaires pour accéder à cette ressource.</p>
          <p class="error-details">Cela peut être dû à :</p>
          <ul class="error-reasons">
            <li>Un problème de configuration de votre espace de coworking</li>
            <li>Des permissions insuffisantes</li>
            <li>Une session expirée</li>
          </ul>
        </div>

        <!-- Actions -->
        <div class="error-actions">
          <button 
            nz-button 
            nzType="primary" 
            nzSize="large"
            (click)="goHome()"
            class="primary-button"
          >
            <nz-icon nzType="home" nzTheme="outline"></nz-icon>
            Retour à l'accueil
          </button>
          
          <button 
            nz-button 
            nzType="default" 
            nzSize="large"
            (click)="logout()"
            class="secondary-button"
          >
            <nz-icon nzType="logout" nzTheme="outline"></nz-icon>
            Se déconnecter
          </button>
        </div>

        <!-- Footer -->
        <div class="error-footer">
          <p>Si le problème persiste, contactez votre administrateur.</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .unauthorized-container {
      min-height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      padding: 40px 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1000;
    }

    .unauthorized-content {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 48px 40px;
      width: 100%;
      max-width: 500px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .error-icon {
      margin-bottom: 24px;
    }

    .error-icon nz-icon {
      font-size: 64px;
      color: #ef4444;
    }

    .error-message h1 {
      color: #1f2937;
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 16px 0;
      line-height: 1.2;
    }

    .error-message p {
      color: #6b7280;
      font-size: 16px;
      line-height: 1.5;
      margin: 0 0 16px 0;
    }

    .error-details {
      font-weight: 500;
      color: #374151 !important;
      margin-top: 24px !important;
    }

    .error-reasons {
      text-align: left;
      margin: 16px 0 32px 0;
      padding-left: 20px;
    }

    .error-reasons li {
      color: #6b7280;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 8px;
    }

    .error-actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 32px;
    }

    .primary-button {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 12px;
      background: #3b82f6 !important;
      border-color: #3b82f6 !important;
    }

    .primary-button:hover {
      background: #2563eb !important;
      border-color: #2563eb !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .secondary-button {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 12px;
      background: #f8fafc !important;
      border-color: #e2e8f0 !important;
      color: #64748b !important;
    }

    .secondary-button:hover {
      background: #f1f5f9 !important;
      border-color: #cbd5e1 !important;
      color: #475569 !important;
      transform: translateY(-1px);
    }

    .primary-button nz-icon,
    .secondary-button nz-icon {
      margin-right: 8px;
    }

    .error-footer {
      padding-top: 24px;
      border-top: 1px solid #f1f5f9;
    }

    .error-footer p {
      color: #9ca3af;
      font-size: 12px;
      margin: 0;
    }

    @media (max-width: 480px) {
      .unauthorized-container {
        padding: 20px 16px;
      }
      
      .unauthorized-content {
        padding: 32px 24px;
      }
      
      .error-message h1 {
        font-size: 24px;
      }
      
      .error-icon nz-icon {
        font-size: 48px;
      }
    }
  `]
})
export class UnauthorizedComponent {

  constructor(
    private router: Router,
    private authService: AuthService
  ) {}

  goHome() {
    this.router.navigate(['/welcome']);
  }

  logout() {
    this.authService.logout();
  }
}
