<!-- Backdrop -->
<div class="sites-backdrop"
     [class.sidebar-collapsed]="sidebarCollapsed"
     *ngIf="isExpanded"
     (click)="closePanel()"
     [@fadeIn]></div>

<div class="sites-panel" [class.expanded]="isExpanded">
  <!-- Bouton de toggle -->
  <div class="sites-toggle" (click)="togglePanel()">
    <div class="current-site-info">
      <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
      <span class="site-name">{{ currentSite?.name || 'Sélectionner un site' }}</span>
      <span class="site-city">{{ currentSite?.city }}</span>
    </div>
    <nz-icon
      [nzType]="isExpanded ? 'up' : 'down'"
      nzTheme="outline"
      class="toggle-icon"
    ></nz-icon>
  </div>

  <!-- Panel étendu -->
  <div class="sites-content" *ngIf="isExpanded" [@slideDown]>
    <div class="sites-header">
      <h3>Vos sites</h3>
    </div>

    <div class="sites-grid">
      <!-- Carte d'ajout de site -->
      <div class="site-card add-site-card" (click)="openAddSiteModal()">
        <div class="add-site-content">
          <div class="add-site-icon">
            <nz-icon nzType="plus" nzTheme="outline"></nz-icon>
          </div>
          <span class="add-site-text">Ajouter un site</span>
        </div>
      </div>

      <!-- Sites existants -->
      <div
        *ngFor="let site of sites"
        class="site-card"
        [class.active]="currentSite?.id === site.id"
        [class.inactive]="!site.isActive"
        (click)="selectSite(site)"
      >
        <div class="site-icon">
          <nz-icon nzType="home" nzTheme="outline"></nz-icon>
          <div class="site-status" [class.active]="site.isActive">
            <nz-icon [nzType]="site.isActive ? 'check-circle' : 'pause-circle'" nzTheme="fill"></nz-icon>
          </div>
        </div>

        <div class="site-info">
          <h4>{{ site.name }}</h4>
          <p class="site-location">
            <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
            {{ site.city }}, {{ site.country }}
          </p>
        </div>

        <div class="site-actions" (click)="$event.stopPropagation()">
          <div nz-dropdown [nzDropdownMenu]="siteMenu" nzTrigger="click">
            <button nz-button nzType="text" nzSize="small">
              <nz-icon nzType="more" nzTheme="outline"></nz-icon>
            </button>
          </div>
          <nz-dropdown-menu #siteMenu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-item (click)="editSite(site)">
                <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
                Modifier
              </li>
              <li nz-menu-item
                  (click)="toggleSiteStatus(site)"
                  [class.disabled]="site.isActive && isCurrentSite(site)"
                  [title]="site.isActive && isCurrentSite(site) ? 'Impossible de désactiver le site actuellement sélectionné' : ''">
                <nz-icon [nzType]="site.isActive ? 'pause' : 'play-circle'" nzTheme="outline"></nz-icon>
                {{ site.isActive ? 'Désactiver' : 'Activer' }}
                <span *ngIf="site.isActive && isCurrentSite(site)" class="current-site-indicator"> (Actuel)</span>
              </li>
              <li nz-menu-divider></li>
              <li nz-menu-item (click)="deleteSite(site)" class="danger">
                <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                Supprimer
              </li>
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal d'ajout/modification de site -->
<nz-modal
  [(nzVisible)]="isModalVisible"
  [nzTitle]="modalTitle"
  [nzFooter]="null"
  (nzOnCancel)="closeModal()"
  nzWidth="600px"
>
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="siteForm" (ngSubmit)="saveSite()">
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Nom du site</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez saisir le nom du site">
              <input nz-input formControlName="name" placeholder="Ex: Workeem Paris" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label>Ville</nz-form-label>
            <nz-form-control>
              <input nz-input formControlName="city" placeholder="Ex: Paris" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label>Pays</nz-form-label>
            <nz-form-control>
              <input nz-input formControlName="country" placeholder="Ex: France" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label>Statut</nz-form-label>
            <nz-form-control>
              <nz-switch formControlName="isActive" nzCheckedChildren="Actif" nzUnCheckedChildren="Inactif"></nz-switch>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <nz-form-item>
        <nz-form-label>Adresse</nz-form-label>
        <nz-form-control>
          <textarea nz-input formControlName="address" placeholder="Adresse complète du site (optionnel)" rows="3"></textarea>
        </nz-form-control>
      </nz-form-item>

      <div class="modal-actions">
        <button nz-button type="button" (click)="closeModal()">Annuler</button>
        <button nz-button nzType="primary" type="submit" [nzLoading]="saving" [disabled]="!siteForm.valid">
          {{ editingSite ? 'Modifier' : 'Ajouter' }}
        </button>
      </div>
    </form>
  </ng-container>
</nz-modal>
