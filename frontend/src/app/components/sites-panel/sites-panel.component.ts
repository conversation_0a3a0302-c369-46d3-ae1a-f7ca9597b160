import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
// import { Subject, takeUntil } from 'rxjs'; // Plus nécessaire

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzMenuModule } from 'ng-zorro-antd/menu';

import { SiteService } from '../../services/site.service';
import { AuthService } from '../../services/auth.service';
import { SiteContextService } from '../../services/site-context.service';
import { Site } from '../../models/site.model';
import { slideDownAnimation, fadeInAnimation } from '../../animations/slide.animation';

@Component({
  selector: 'app-sites-panel',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzIconModule,
    NzDropDownModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSwitchModule,
    NzGridModule,
    NzMenuModule
  ],
  templateUrl: './sites-panel.component.html',
  styleUrls: ['./sites-panel.component.css'],
  animations: [slideDownAnimation, fadeInAnimation]
})
export class SitesPanelComponent implements OnInit {

  @Input() sidebarCollapsed = false;

  isExpanded = false;
  sites: Site[] = [];
  currentSite: Site | null = null;

  // Modal
  isModalVisible = false;
  modalTitle = '';
  editingSite: Site | null = null;
  saving = false;

  siteForm: FormGroup;

  constructor(
    private siteService: SiteService,
    private authService: AuthService,
    private siteContext: SiteContextService,
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.siteForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      city: ['', [Validators.maxLength(50)]],
      country: ['', [Validators.maxLength(50)]],
      address: ['', [Validators.maxLength(255)]],
      isActive: [true]
    });
  }

  async ngOnInit() {
    // Ne charger les sites que si l'utilisateur est authentifié
    if (this.authService.isAuthenticated()) {
      await this.loadSites();
      // Charger le site actuel depuis le contexte
      await this.loadCurrentSite();
    } else {
      console.log('🔒 User not authenticated, skipping sites loading');
    }
  }

  async loadSites() {
    try {
      this.sites = await this.siteService.getSites();
    } catch (error) {
      console.error('Erreur lors du chargement des sites:', error);
      this.sites = [];
    }
  }

  async loadCurrentSite() {
    try {
      const currentSite = await this.siteContext.loadCurrentSite();
      if (currentSite) {
        this.currentSite = currentSite;
      } else if (this.sites.length > 0) {
        // Si aucun site n'est sélectionné, prendre le premier actif
        const defaultSite = this.sites.find(site => site.isActive) || this.sites[0];
        if (defaultSite) {
          await this.selectSite(defaultSite);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement du site actuel:', error);
    }
  }



  togglePanel() {
    this.isExpanded = !this.isExpanded;
  }

  closePanel() {
    this.isExpanded = false;
  }

  async selectSite(site: Site) {
    if (!site.isActive) {
      this.message.warning('Ce site est désactivé');
      return;
    }

    try {
      // Sélectionner le site via le contexte (met à jour backend + contexte)
      const selectedSite = await this.siteContext.selectSite(site.id);
      if (selectedSite) {
        this.currentSite = selectedSite;
        this.closePanel();
        this.message.success(`Basculé vers ${site.name}`);
      }
    } catch (error) {
      console.error('Erreur lors de la sélection du site:', error);
      this.message.error('Erreur lors de la sélection du site');
    }
  }

  openAddSiteModal() {
    this.modalTitle = 'Ajouter un nouveau site';
    this.editingSite = null;
    this.siteForm.reset({
      name: '',
      city: '',
      country: '',
      address: '',
      isActive: true
    });
    this.isModalVisible = true;
  }

  editSite(site: Site) {
    this.modalTitle = 'Modifier le site';
    this.editingSite = site;
    this.siteForm.patchValue({
      name: site.name,
      city: site.city,
      country: site.country,
      address: site.address,
      isActive: site.isActive
    });
    this.isModalVisible = true;
  }

  async saveSite() {
    if (this.siteForm.valid) {
      this.saving = true;
      const formValue = this.siteForm.value;

      // S'assurer que tous les champs requis sont présents
      const siteData = {
        name: formValue.name || '',
        city: formValue.city || '',
        country: formValue.country || '',
        address: formValue.address || '',
        isActive: formValue.isActive !== undefined ? formValue.isActive : true
      };

      console.log('Saving site with data:', siteData);

      try {
        if (this.editingSite) {
          // Modification
          await this.siteService.updateSite(this.editingSite.id, siteData);
          this.message.success('Site modifié avec succès');
        } else {
          // Ajout
          await this.siteService.addSite(siteData);
          this.message.success('Site ajouté avec succès');
        }
        await this.loadSites(); // Recharger la liste
        this.closeModal();
      } catch (error) {
        console.error('Error saving site:', error);
        this.message.error(this.editingSite ? 'Erreur lors de la modification du site' : 'Erreur lors de l\'ajout du site');
        this.saving = false;
      }
    } else {
      console.log('Form is invalid:', this.siteForm.errors);
      this.message.error('Veuillez remplir tous les champs requis');
    }
  }

  async toggleSiteStatus(site: Site) {
    // Vérifier si on essaie de désactiver le site actuellement sélectionné
    if (site.isActive && this.isCurrentSite(site)) {
      this.message.warning('Impossible de désactiver le site actuellement sélectionné. Veuillez d\'abord sélectionner un autre site.');
      return;
    }

    try {
      const updatedSite = await this.siteService.toggleSiteStatus(site.id);

      // Mettre à jour la liste locale
      const index = this.sites.findIndex(s => s.id === site.id);
      if (index !== -1) {
        this.sites[index] = updatedSite;
      }

      // Si c'est le site actuel, mettre à jour le contexte
      if (this.currentSite?.id === site.id) {
        this.currentSite = updatedSite;
        this.siteContext.setCurrentSite(updatedSite);
      }

      this.message.success(`Site ${updatedSite.isActive ? 'activé' : 'désactivé'} avec succès`);
    } catch (error: any) {
      console.error('Erreur lors du changement de statut:', error);

      // Gestion spécifique de l'erreur de site actuel
      if (error.error?.code === 'CURRENT_SITE_CANNOT_BE_DEACTIVATED') {
        this.message.error(error.error.message || 'Impossible de désactiver le site actuellement sélectionné');
      } else {
        this.message.error('Erreur lors de la modification du statut');
      }
    }
  }

  async deleteSite(site: Site) {
    if (this.sites.length <= 1) {
      this.message.warning('Vous ne pouvez pas supprimer le dernier site');
      return;
    }

    try {
      await this.siteService.deleteSite(site.id);
      await this.loadSites(); // Recharger la liste
      this.message.success('Site supprimé avec succès');

      // Si le site supprimé était le site actuel, basculer vers le premier site disponible
      if (this.currentSite?.id === site.id) {
        this.currentSite = this.sites.find(s => s.isActive) || this.sites[0] || null;
      }
    } catch (error) {
      this.message.error('Erreur lors de la suppression du site');
    }
  }

  closeModal() {
    this.isModalVisible = false;
    this.saving = false;
    this.editingSite = null;
  }

  /**
   * Vérifier si le site donné est le site actuellement sélectionné
   */
  isCurrentSite(site: Site): boolean {
    return this.currentSite?.id === site.id;
  }
}
