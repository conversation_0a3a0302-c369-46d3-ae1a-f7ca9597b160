<nz-card class="daily-calendar-card" [nzLoading]="loading">
  <div class="calendar-header">
    <div class="header-info">
      <h3 class="calendar-title">
        <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        Planning du jour
      </h3>
      <p class="calendar-date">{{ currentDate | date:'EEEE d MMMM yyyy':'fr' }}</p>
    </div>

    <!-- Légende intégrée dans le header -->
    <div class="header-legend">
      <span class="legend-title">Légende :</span>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color confirmed"></div>
          <span>Confirmé</span>
        </div>
        <div class="legend-item">
          <div class="legend-color pending"></div>
          <span>En attente</span>
        </div>
        <div class="legend-item">
          <div class="legend-color cancelled"></div>
          <span>Annulé</span>
        </div>
      </div>
    </div>

    <div class="header-actions">
      <button nz-button nzType="text" nzSize="small" (click)="refreshCalendar()" class="refresh-btn">
        <nz-icon nzType="reload"></nz-icon>
        Actualiser
      </button>
    </div>
  </div>

  <div class="calendar-container">
    <!-- En-tête des espaces -->
    <div class="spaces-header">
      <div class="time-column-header">Heure</div>
      <div class="space-column" *ngFor="let spaceAvailability of spacesAvailability">
        <div class="space-info">
          <div class="space-name">{{ spaceAvailability.space.name }}</div>
          <div class="space-location">{{ spaceAvailability.space.location }}</div>
          <div class="space-details">
            <span class="space-capacity">
              <nz-icon [nzType]="getSpaceCapacityIcon(spaceAvailability.space.capacity)"></nz-icon>
              {{ spaceAvailability.space.capacity }}
            </span>
            <span class="space-type">
              <nz-icon [nzType]="getSpaceIcon(spaceAvailability.space.type)"></nz-icon>
            </span>
            <span class="space-amenities" *ngIf="spaceAvailability.space.amenities?.length">
              <nz-icon nzType="wifi" *ngIf="spaceAvailability.space.amenities.includes('wifi')"></nz-icon>
              <nz-icon nzType="video-camera" *ngIf="spaceAvailability.space.amenities.includes('projector')"></nz-icon>
              <nz-icon nzType="sound" *ngIf="spaceAvailability.space.amenities.includes('audio')"></nz-icon>
              <nz-icon nzType="coffee" *ngIf="spaceAvailability.space.amenities.includes('coffee')"></nz-icon>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Grille du calendrier -->
    <div class="calendar-grid">
      <div class="time-slots">
        <div class="time-slot" *ngFor="let timeSlot of timeSlots">
          {{ timeSlot.hour }}
        </div>
      </div>

      <div class="spaces-grid">
        <div class="space-column" *ngFor="let spaceAvailability of spacesAvailability">
          <div class="time-cell" *ngFor="let timeSlot of timeSlots; let i = index">
            <ng-container *ngIf="isTimeSlotReserved(spaceAvailability.space, timeSlot) as reservation; else availableSlot">
              <!-- Afficher seulement le premier créneau d'une réservation -->
              <div *ngIf="isFirstSlotOfReservation(spaceAvailability.space, timeSlot, reservation)"
                   class="reservation-block"
                   [style.background-color]="getReservationColor(reservation)"
                   [style.height.px]="getReservationHeight(reservation)"
                   [style.top.px]="getReservationTopOffset(reservation)"
                   [nz-tooltip]="getReservationTooltip(reservation)"
                   nzTooltipPlacement="top"
                   (click)="onReservationClick(reservation)">
                <div class="reservation-content">
                  <div class="reservation-title">{{ reservation.purpose || 'Réservation' }}</div>
                  <div class="reservation-member">{{ reservation.userName }}</div>
                  <div class="reservation-time">{{ formatReservationTime(reservation) }}</div>
                </div>
              </div>
            </ng-container>

            <ng-template #availableSlot>
              <div class="available-slot"
                   [class.selected]="isSlotSelected(spaceAvailability.space, timeSlot)"
                   (mousedown)="onSlotMouseDown(spaceAvailability.space, timeSlot, $event)"
                   (mouseup)="onSlotMouseUp(spaceAvailability.space, timeSlot, $event)"
                   (mouseover)="onSlotMouseOver(spaceAvailability.space, timeSlot, $event)"
                   (touchstart)="onSlotTouchStart(spaceAvailability.space, timeSlot, $event)"
                   (touchend)="onSlotTouchEnd(spaceAvailability.space, timeSlot, $event)"
                   (touchmove)="onSlotTouchMove(spaceAvailability.space, timeSlot, $event)">
                <span class="available-text">Libre</span>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>


</nz-card>
