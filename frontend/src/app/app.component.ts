import { Component, OnInit, HostListener } from '@angular/core';
import { RouterOutlet, RouterLink, Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter } from 'rxjs';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NotificationsComponent } from './components/notifications/notifications.component';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { AuthService } from './services/auth.service';
import { SitesPanelComponent } from './components/sites-panel/sites-panel.component';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    NzIconModule,
    NzLayoutModule,
    NzMenuModule,
    NzButtonModule,
    NzAvatarModule,
    NzDropDownModule,
    NzDividerModule,
    NzMessageModule,
    NzModalModule,
    NotificationsComponent,
    SitesPanelComponent
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  private readonly SIDEBAR_STATE_KEY = 'workeem_sidebar_collapsed';

  isCollapsed = false;
  isAuthenticated = false;
  currentUser: string | null = null;
  showQuickActions = false;
  private hideTimeout: any;

  // Mobile responsive properties
  isMobile = false;
  sidebarOpen = false;

  constructor(
    private router: Router,
    private authService: AuthService,
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  ngOnInit() {
    // Restaurer l'état du sidebar depuis localStorage
    this.restoreSidebarState();

    // Détecter si on est sur mobile au démarrage
    this.checkIfMobile();

    // Vérifier l'état d'authentification Keycloak
    this.isAuthenticated = this.authService.isAuthenticated();
    this.currentUser = this.authService.getUsername() || null;

    // Fermer le sidebar mobile lors des changements de route
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        if (this.isMobile && this.sidebarOpen) {
          this.sidebarOpen = false;
        }
      });
  }

  // Listener pour détecter les changements de taille d'écran
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkIfMobile();
    // Fermer le sidebar mobile si on passe en mode desktop
    if (!this.isMobile && this.sidebarOpen) {
      this.sidebarOpen = false;
    }
  }

  // Getters pour les templates
  get logoSrc() {
    if (this.isCollapsed) {
      return 'assets/images/logo-min.png';
    }
    return 'assets/images/logo.png';
  }

  get tenantName(): string {
    return 'Workeem';
  }

  get managerName(): string {
    return this.currentUser || 'Utilisateur';
  }

  get userAvatar(): string {
    return 'assets/images/avatar-default.png';
  }

  // Méthodes utilitaires
  isReservationRoute(): boolean {
    const url = this.router.url;
    return url.includes('/reservations') || url.includes('/reservation-form');
  }

  isLoginRoute(): boolean {
    // Pages dédiées sans layout (sidebar/topnav)
    return this.router.url === '/tenant-selection';
  }

  isStandalonePage(): boolean {
    // Pages qui ne doivent pas avoir le layout principal
    const standalonePages = ['/tenant-selection', '/unauthorized'];
    return standalonePages.includes(this.router.url);
  }

  // Méthodes d'authentification
  onLogout() {
    this.modal.confirm({
      nzTitle: 'Confirmer la déconnexion',
      nzContent: 'Êtes-vous sûr de vouloir vous déconnecter ?',
      nzOkText: 'Se déconnecter',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.performLogout();
      }
    });
  }

  private performLogout() {
    // Keycloak gère la déconnexion directement
    this.authService.logout();
  }

  // Méthodes de gestion du sidebar
  private restoreSidebarState() {
    const savedState = localStorage.getItem(this.SIDEBAR_STATE_KEY);
    if (savedState !== null) {
      this.isCollapsed = JSON.parse(savedState);
    }
  }

  private saveSidebarState() {
    localStorage.setItem(this.SIDEBAR_STATE_KEY, JSON.stringify(this.isCollapsed));
  }

  toggleSidebar() {
    if (this.isMobile) {
      // Sur mobile, on gère l'ouverture/fermeture du sidebar
      this.sidebarOpen = !this.sidebarOpen;
    } else {
      // Sur desktop, on gère le collapse/expand
      this.isCollapsed = !this.isCollapsed;
      this.saveSidebarState();
    }
  }

  onSidebarCollapsedChange(collapsed: boolean) {
    this.isCollapsed = collapsed;
    this.saveSidebarState();
  }

  // Méthodes pour la gestion mobile
  private checkIfMobile() {
    this.isMobile = window.innerWidth <= 768;
  }

  closeMobileSidebar() {
    if (this.isMobile) {
      this.sidebarOpen = false;
    }
  }

  // Méthode pour obtenir le titre de la page actuelle
  getPageTitle(): string {
    const url = this.router.url;
    const titleMap: { [key: string]: string } = {
      '/welcome': 'Dashboard',
      '/members': 'Membres',
      '/subscriptions': 'Abonnements',
      '/spaces': 'Espaces',
      '/reservations': 'Réservations',
      '/billing': 'Facturation',
      '/statistics': 'Stats'
    };

    // Chercher une correspondance exacte ou partielle
    for (const [route, title] of Object.entries(titleMap)) {
      if (url.startsWith(route)) {
        return title;
      }
    }

    return 'Workeem';
  }

  // Méthodes pour le menu de raccourcis
  hideQuickActions() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }
    this.hideTimeout = setTimeout(() => {
      this.showQuickActions = false;
    }, 300); // Délai de 300ms pour permettre le passage de la souris
  }

  showQuickActionsMenu() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }
    this.showQuickActions = true;
  }

  // Méthodes de navigation pour les actions rapides
  navigateToNewMember() {
    this.router.navigate(['/members/new']);
    this.showQuickActions = false;
  }

  navigateToNewReservation() {
    this.router.navigate(['/reservation-form']);
    this.showQuickActions = false;
  }

  navigateToNewSpace() {
    this.router.navigate(['/spaces/new']);
    this.showQuickActions = false;
  }

  navigateToNewInvoice() {
    this.router.navigate(['/billing']);
    this.showQuickActions = false;
    // TODO: Ouvrir directement le modal de nouvelle facture
  }

  navigateToNewSubscription() {
    this.router.navigate(['/subscriptions/new']);
    this.showQuickActions = false;
  }

  navigateToSpaceCalendar() {
    this.router.navigate(['/spaces/calendar']);
    this.showQuickActions = false;
  }

  // Méthodes de navigation
  navigateToProfile() {
    // TODO: Implémenter la navigation vers le profil utilisateur
    this.message.info('Fonctionnalité de profil à venir');
  }

  navigateToSettings() {
    // TODO: Implémenter la navigation vers les paramètres
    this.message.info('Fonctionnalité de paramètres à venir');
  }
}
