import { Injectable } from '@angular/core';
import {
  CanActivate,
  CanActivateChild,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router
} from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  private checkAuth(url: string): boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const tenantId = localStorage.getItem('workeem_tenant_id');

    console.log('🔐 AuthGuard check:', { url, isAuthenticated, tenantId });

    // Si pas authentifié et pas de tenant, toujours rediriger vers tenant-selection
    if (!isAuthenticated && !tenantId) {
      console.log('❌ Not authenticated and no tenant, redirecting to tenant-selection');
      this.router.navigate(['/tenant-selection']);
      return false;
    }

    // Si authentifié mais pas de tenant, rediriger vers tenant-selection
    if (isAuthenticated && !tenantId) {
      console.log('✅ Authenticated but no tenant, redirecting to tenant-selection');
      this.router.navigate(['/tenant-selection']);
      return false;
    }

    // Si authentifié et tenant sélectionné, autoriser l'accès
    if (isAuthenticated && tenantId) {
      console.log('✅ Authenticated and tenant selected, allowing access');
      return true;
    }

    // Si pas authentifié mais tenant sélectionné, déclencher la connexion Keycloak
    if (!isAuthenticated && tenantId) {
      console.log('🔑 Tenant selected but not authenticated, triggering Keycloak login');
      this.authService.login();
      return false;
    }

    // Fallback
    console.log('🤔 Unexpected state, redirecting to tenant-selection');
    this.router.navigate(['/tenant-selection']);
    return false;
  }
}
