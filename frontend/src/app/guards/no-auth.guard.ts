import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class NoAuthGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): boolean {
    // Si l'utilisateur est déjà authentifié, rediriger vers l'app
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/welcome']);
      return false;
    }
    
    // Si pas authentifié, autoriser l'accès à la page de sélection de tenant
    return true;
  }
}
