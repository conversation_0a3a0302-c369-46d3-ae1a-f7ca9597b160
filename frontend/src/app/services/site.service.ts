import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import { Site, SiteStats } from '../models/site.model';

@Injectable({
  providedIn: 'root'
})
export class SiteService {
  private readonly baseUrl = 'http://localhost:8080/api/sites';

  constructor(private http: HttpClient) {}

  async getSites(): Promise<Site[]> {
    return await firstValueFrom(this.http.get<Site[]>(this.baseUrl));
  }

  async getSiteById(id: string): Promise<Site> {
    return await firstValueFrom(this.http.get<Site>(`${this.baseUrl}/${id}`));
  }

  async addSite(site: any): Promise<Site> {
    return await firstValueFrom(this.http.post<Site>(this.baseUrl, site));
  }

  async updateSite(siteId: string, updates: any): Promise<Site> {
    return await firstValueFrom(this.http.put<Site>(`${this.baseUrl}/${siteId}`, updates));
  }

  async deleteSite(siteId: string): Promise<void> {
    await firstValueFrom(this.http.delete(`${this.baseUrl}/${siteId}`));
  }

  async getSiteStats(siteId: string): Promise<SiteStats> {
    // Mock data pour les statistiques
    return {
      occupancyRate: 85,
      monthlyRevenue: 12500
    };
  }

  /**
   * Toggle site status (activate/deactivate)
   */
  async toggleSiteStatus(siteId: string): Promise<Site> {
    return await firstValueFrom(
      this.http.patch<Site>(`${this.baseUrl}/${siteId}/toggle-status`, {})
    );
  }

  /**
   * Select a site as current
   */
  async selectSite(siteId: string): Promise<Site> {
    return await firstValueFrom(
      this.http.post<Site>(`${this.baseUrl}/${siteId}/select`, {})
    );
  }

  /**
   * Get current selected site
   */
  async getCurrentSite(): Promise<Site | null> {
    try {
      return await firstValueFrom(
        this.http.get<Site>(`${this.baseUrl}/current`)
      );
    } catch (error: any) {
      if (error.status === 204) {
        // No content - no current site
        return null;
      }
      throw error;
    }
  }
}
