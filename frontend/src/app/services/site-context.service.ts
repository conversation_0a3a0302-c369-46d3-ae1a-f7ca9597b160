import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Site } from '../models/site.model';
import { SiteService } from './site.service';

@Injectable({
  providedIn: 'root'
})
export class SiteContextService {
  private currentSiteSubject = new BehaviorSubject<Site | null>(null);
  public currentSite$ = this.currentSiteSubject.asObservable();

  constructor(private siteService: SiteService) {}

  /**
   * Get current site from context
   */
  getCurrentSite(): Site | null {
    return this.currentSiteSubject.value;
  }

  /**
   * Set current site in context
   */
  setCurrentSite(site: Site | null): void {
    this.currentSiteSubject.next(site);
    console.log('🏢 Current site updated in context:', site?.name || 'None');
  }

  /**
   * Load current site from backend and set in context
   */
  async loadCurrentSite(): Promise<Site | null> {
    try {
      const currentSite = await this.siteService.getCurrentSite();
      this.setCurrentSite(currentSite);
      return currentSite;
    } catch (error) {
      console.error('Error loading current site:', error);
      this.setCurrentSite(null);
      return null;
    }
  }

  /**
   * Select a site (update backend + context)
   */
  async selectSite(siteId: string): Promise<Site | null> {
    try {
      const selectedSite = await this.siteService.selectSite(siteId);
      this.setCurrentSite(selectedSite);
      console.log('✅ Site selected and context updated:', selectedSite.name);
      return selectedSite;
    } catch (error) {
      console.error('Error selecting site:', error);
      throw error;
    }
  }

  /**
   * Clear current site from context
   */
  clearCurrentSite(): void {
    this.setCurrentSite(null);
  }

  /**
   * Check if a site is currently selected
   */
  hasSiteSelected(): boolean {
    return this.currentSiteSubject.value !== null;
  }

  /**
   * Get current site ID
   */
  getCurrentSiteId(): string | null {
    return this.currentSiteSubject.value?.id || null;
  }

  /**
   * Get current site name
   */
  getCurrentSiteName(): string | null {
    return this.currentSiteSubject.value?.name || null;
  }
}
