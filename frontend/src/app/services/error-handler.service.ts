import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {

  constructor(
    private router: Router,
    private message: NzMessageService
  ) {}

  handleHttpError(status: number, url: string, message?: string) {
    console.log(`🚫 HTTP Error ${status} for ${url}:`, message);

    switch (status) {
      case 401:
        this.handleUnauthorized();
        break;
      case 403:
        this.handleForbidden();
        break;
      case 404:
        this.handleNotFound();
        break;
      case 500:
        this.handleServerError();
        break;
      default:
        this.handleGenericError(status);
    }
  }

  private handleUnauthorized() {
    // Ne pas rediriger si on est déjà sur une page de gestion d'auth
    const currentUrl = this.router.url;
    const authPages = ['/unauthorized', '/tenant-selection'];

    if (!authPages.some(page => currentUrl.includes(page))) {
      this.message.error('Session expirée ou accès non autorisé');
      this.router.navigate(['/unauthorized']);
    }
  }

  private handleForbidden() {
    const currentUrl = this.router.url;
    const authPages = ['/unauthorized', '/tenant-selection'];

    if (!authPages.some(page => currentUrl.includes(page))) {
      this.message.error('Accès interdit - Permissions insuffisantes');
      this.router.navigate(['/unauthorized']);
    }
  }

  private handleNotFound() {
    this.message.error('Ressource non trouvée');
  }

  private handleServerError() {
    this.message.error('Erreur serveur - Veuillez réessayer plus tard');
  }

  private handleGenericError(status: number) {
    this.message.error(`Erreur ${status} - Une erreur inattendue s'est produite`);
  }
}
